"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_control_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/control-sidebar */ \"(app-pages-browser)/./components/control-sidebar.tsx\");\n/* harmony import */ var _components_visualization_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/visualization-panel */ \"(app-pages-browser)/./components/visualization-panel.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nfunction Home() {\n    _s();\n    const [showQueryFlow, setShowQueryFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSystemInitialized, setIsSystemInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Welcome to DormIQ Analytics. I can help you understand your Building with natural language. Please initialize the system to begin.\",\n            timestamp: new Date()\n        }\n    ]);\n    const handleInitializeSystem = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/initialize\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setIsSystemInitialized(true);\n                const newMessage = {\n                    id: Date.now().toString(),\n                    role: \"system\",\n                    content: result.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        newMessage\n                    ]);\n            } else {\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: \"system\",\n                    content: \"Initialization failed: \".concat(result.message),\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            console.error(\"Initialization error:\", error);\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: \"system\",\n                content: \"Failed to connect to backend. Please ensure the backend server is running on port 8000.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/chat\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    conversation_id: \"default\"\n                })\n            });\n            const result = await response.json();\n            const aiMessage = {\n                id: result.id || (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: result.content || \"I apologize, but I couldn't process your request at the moment.\",\n                timestamp: new Date(result.timestamp) || new Date(),\n                query_flow: result.query_flow || undefined\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I'm sorry, but I'm having trouble connecting to the backend. Please check if the server is running.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        }\n    };\n    const handleClearChat = ()=>{\n        setMessages([]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-black-blue\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            defaultOpen: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_control_sidebar__WEBPACK_IMPORTED_MODULE_3__.ControlSidebar, {\n                        showQueryFlow: showQueryFlow,\n                        setShowQueryFlow: setShowQueryFlow,\n                        isSystemInitialized: isSystemInitialized,\n                        onInitializeSystem: handleInitializeSystem,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-border bg-black/50 backdrop-blur-sm p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"grid w-fit grid-cols-2 bg-black/60 border border-custom-blue/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"chat\",\n                                                className: \"data-[state=active]:bg-custom-blue data-[state=active]:text-white\",\n                                                children: \"Chat Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"visualization\",\n                                                className: \"data-[state=active]:bg-custom-blue data-[state=active]:text-white\",\n                                                children: \"Data Visualization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"chat\",\n                                    className: \"flex-1 m-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_2__.ChatInterface, {\n                                        messages: messages,\n                                        onSendMessage: handleSendMessage,\n                                        onClearChat: handleClearChat,\n                                        showQueryFlow: showQueryFlow,\n                                        isSystemInitialized: isSystemInitialized\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"visualization\",\n                                    className: \"flex-1 m-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_panel__WEBPACK_IMPORTED_MODULE_4__.VisualizationPanel, {\n                                        isSystemInitialized: isSystemInitialized\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"W7WFOqatckJvlnTXeaSNSTzT9F8=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});