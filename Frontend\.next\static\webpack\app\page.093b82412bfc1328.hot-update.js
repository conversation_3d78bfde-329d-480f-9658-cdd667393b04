"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_control_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/control-sidebar */ \"(app-pages-browser)/./components/control-sidebar.tsx\");\n/* harmony import */ var _components_visualization_panel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/visualization-panel */ \"(app-pages-browser)/./components/visualization-panel.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nfunction Home() {\n    _s();\n    const [showQueryFlow, setShowQueryFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSystemInitialized, setIsSystemInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            role: \"assistant\",\n            content: \"Welcome to DormIQ Analytics. I can help you query your graph and time series databases with natural language. Please initialize the system to begin.\",\n            timestamp: new Date()\n        }\n    ]);\n    const handleInitializeSystem = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/initialize\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setIsSystemInitialized(true);\n                const newMessage = {\n                    id: Date.now().toString(),\n                    role: \"system\",\n                    content: result.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        newMessage\n                    ]);\n            } else {\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: \"system\",\n                    content: \"Initialization failed: \".concat(result.message),\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            console.error(\"Initialization error:\", error);\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: \"system\",\n                content: \"Failed to connect to backend. Please ensure the backend server is running on port 8000.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/chat\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    conversation_id: \"default\"\n                })\n            });\n            const result = await response.json();\n            const aiMessage = {\n                id: result.id || (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: result.content || \"I apologize, but I couldn't process your request at the moment.\",\n                timestamp: new Date(result.timestamp) || new Date(),\n                query_flow: result.query_flow || undefined\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiMessage\n                ]);\n        } catch (error) {\n            console.error(\"Chat error:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: \"assistant\",\n                content: \"I'm sorry, but I'm having trouble connecting to the backend. Please check if the server is running.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        }\n    };\n    const handleClearChat = ()=>{\n        setMessages([]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-black-blue\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            defaultOpen: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_control_sidebar__WEBPACK_IMPORTED_MODULE_3__.ControlSidebar, {\n                        showQueryFlow: showQueryFlow,\n                        setShowQueryFlow: setShowQueryFlow,\n                        isSystemInitialized: isSystemInitialized,\n                        onInitializeSystem: handleInitializeSystem,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-border bg-black/50 backdrop-blur-sm p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"grid w-fit grid-cols-2 bg-black/60 border border-custom-blue/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"chat\",\n                                                className: \"data-[state=active]:bg-custom-blue data-[state=active]:text-white\",\n                                                children: \"Chat Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"visualization\",\n                                                className: \"data-[state=active]:bg-custom-blue data-[state=active]:text-white\",\n                                                children: \"Data Visualization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"chat\",\n                                    className: \"flex-1 m-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_2__.ChatInterface, {\n                                        messages: messages,\n                                        onSendMessage: handleSendMessage,\n                                        onClearChat: handleClearChat,\n                                        showQueryFlow: showQueryFlow,\n                                        isSystemInitialized: isSystemInitialized\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"visualization\",\n                                    className: \"flex-1 m-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_visualization_panel__WEBPACK_IMPORTED_MODULE_4__.VisualizationPanel, {\n                                        isSystemInitialized: isSystemInitialized\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\app\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"UlDimm0MEcwR+gja1MJVp5ciKig=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-flow-panel */ \"(app-pages-browser)/./components/query-flow-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, onSendMessage, onClearChat, showQueryFlow, isSystemInitialized } = param;\n    _s();\n    const [selectedQueryFlow, setSelectedQueryFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (scrollAreaRef.current) {\n                scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (input.trim() && isSystemInitialized) {\n            onSendMessage(input.trim());\n            setInput(\"\");\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-custom-blue/20 bg-black/50 backdrop-blur-sm p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: \"DormIQ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-custom-blue-light\",\n                                    children: \"When your building speaks, we listen.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"gap-1 border-custom-blue/50 text-custom-blue-light\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Graph DB\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"gap-1 border-custom-blue/50 text-custom-blue-light\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Time Series\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onClearChat,\n                                    className: \"text-gray-400 hover:text-white hover:bg-red-500/20 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Clear\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                ref: scrollAreaRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(message.role === \"system\" ? \"bg-amber-500/20 text-amber-400 border border-amber-500/50\" : \"bg-custom-blue/20 text-custom-blue-light border border-custom-blue/50\"),\n                                    children: message.role === \"system\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 48\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 83\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"max-w-[70%] p-4 \".concat(message.role === \"user\" ? \"bg-custom-blue text-white border-custom-blue/50 shadow-lg\" : message.role === \"system\" ? \"bg-amber-500/10 border-amber-500/30 text-white\" : \"bg-black/40 border-custom-blue/20 text-white\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-3 bg-black/20 rounded-lg border border-custom-blue/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-custom-blue-light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-custom-blue-light\",\n                                                                children: \"Query Execution Flow\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 ml-auto\",\n                                                                children: [\n                                                                    message.query_flow.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-green-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 144,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Success\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-red-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 149,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Failed\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400 ml-2\",\n                                                                        children: [\n                                                                            message.query_flow.execution_time.toFixed(2),\n                                                                            \"s\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: message.query_flow.databases_queried.length > 0 ? [\n                                                            ...new Set(message.query_flow.databases_queried)\n                                                        ].map((db, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs px-2 py-0 h-5 border-custom-blue/30 text-custom-blue-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    db\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 29\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs px-2 py-0 h-5 border-purple-500/30 text-purple-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"LLM Knowledge\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            message.query_flow.processing_steps.length,\n                                                            \" steps: \",\n                                                            message.query_flow.processing_steps.map((step)=>step.step.replace('_', ' ')).join(' → ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs \".concat(message.role === \"user\" ? \"text-blue-200\" : \"text-gray-400\"),\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedQueryFlow(message.query_flow),\n                                                        className: \"h-6 px-2 text-xs text-custom-blue-light hover:text-custom-blue hover:bg-custom-blue/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-black/40 rounded-full flex items-center justify-center border border-custom-blue/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 text-custom-blue-light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    placeholder: isSystemInitialized ? \"Ask me about your dormitory data...\" : \"Please initialize the system first\",\n                                    disabled: !isSystemInitialized,\n                                    className: \"flex-1 bg-black/40 border-custom-blue/30 text-white placeholder:text-gray-400 focus:border-custom-blue focus:ring-custom-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: !input.trim() || !isSystemInitialized,\n                                    className: \"bg-custom-blue hover:bg-custom-blue-dark px-6 border border-custom-blue/50 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        !isSystemInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-amber-400 mt-2\",\n                            children: \"Initialize the system from the control panel to start querying\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            selectedQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/30 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Query Execution Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSelectedQueryFlow(null),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__.QueryFlowPanel, {\n                            queryFlow: selectedQueryFlow,\n                            isVisible: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"OMVXCnMtObGtahIhyRKMJLAwMek=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/control-sidebar.tsx":
/*!****************************************!*\
  !*** ./components/control-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ControlSidebar: () => (/* binding */ ControlSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Loader2,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ ControlSidebar auto */ \n\n\n\n\n\n\n\nfunction ControlSidebar(param) {\n    let { showQueryFlow, setShowQueryFlow, isSystemInitialized, onInitializeSystem, isLoading = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {\n        className: \"border-r border-custom-blue/20 bg-black/80 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarHeader, {\n                className: \"p-6 border-b border-custom-blue/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-gradient-blue-black rounded-xl flex items-center justify-center shadow-lg border border-custom-blue/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-bold text-white text-xl\",\n                                    children: \"Endeavour\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-custom-blue-light\",\n                                    children: \"Intelligent AI Assistant \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarContent, {\n                className: \"px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroup, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupLabel, {\n                                className: \"text-white font-medium text-sm uppercase tracking-wider\",\n                                children: \"System Control\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: onInitializeSystem,\n                                            disabled: isSystemInitialized || isLoading,\n                                            className: \"w-full justify-start gap-2 bg-custom-blue hover:bg-custom-blue-dark text-white border border-custom-blue/50 shadow-lg\",\n                                            size: \"sm\",\n                                            children: [\n                                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, this) : isSystemInitialized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isLoading ? \"Initializing...\" : isSystemInitialized ? \"System Ready\" : \"Initialize System\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"query-flow\",\n                                                    className: \"text-sm text-white\",\n                                                    children: \"Query Flow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                    id: \"query-flow\",\n                                                    checked: showQueryFlow,\n                                                    onCheckedChange: setShowQueryFlow\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                        className: \"my-4 bg-custom-blue/20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroup, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupLabel, {\n                                className: \"text-white font-medium text-sm uppercase tracking-wider\",\n                                children: \"Database Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarGroupContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuButton, {\n                                                className: \"w-full justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20 hover:bg-black/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 text-custom-blue-light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                                lineNumber: 95,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-white\",\n                                                                children: \"Graph DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: isSystemInitialized ? \"default\" : \"secondary\",\n                                                        className: isSystemInitialized ? \"bg-custom-blue/20 text-custom-blue-light border-custom-blue/50\" : \"bg-gray-800 text-gray-400\",\n                                                        children: isSystemInitialized ? \"Connected\" : \"Offline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarMenuButton, {\n                                                className: \"w-full justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20 hover:bg-black/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-custom-blue-light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-white\",\n                                                                children: \"Time Series DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: isSystemInitialized ? \"default\" : \"secondary\",\n                                                        className: isSystemInitialized ? \"bg-custom-blue/20 text-custom-blue-light border-custom-blue/50\" : \"bg-gray-800 text-gray-400\",\n                                                        children: isSystemInitialized ? \"Connected\" : \"Offline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_1__.SidebarFooter, {\n                className: \"p-4 border-t border-custom-blue/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 text-xs text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Powered by\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 font-semibold text-custom-blue-light\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-custom-blue rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-xs font-bold\",\n                                        children: \"E\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                \"Endeavour\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\control-sidebar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = ControlSidebar;\nvar _c;\n$RefreshReg$(_c, \"ControlSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/control-sidebar.tsx\n"));

/***/ })

});