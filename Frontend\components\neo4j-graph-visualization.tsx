'use client'

import React, { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Loader2, <PERSON>freshC<PERSON>, ZoomIn, ZoomOut, RotateCcw } from "lucide-react"

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false })

interface GraphNode {
  id: string
  type: 'Room' | 'ACUnit' | 'Sensor'
  subtype?: 'dormitory' | 'mechanical' | 'temperature' | 'occupancy'
  properties: Record<string, any>
  x?: number
  y?: number
}

interface GraphEdge {
  source: string
  target: string
  relationship: string
}

interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

export default function Neo4jGraphVisualization() {
  const [graphData, setGraphData] = useState<GraphData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [plotData, setPlotData] = useState<any[]>([])
  const [layout, setLayout] = useState<any>({})

  const fetchGraphData = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/visualization/graph-structure')
      if (!response.ok) {
        throw new Error('Failed to fetch graph data')
      }
      const data = await response.json()
      setGraphData(data)
      generatePlotData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      // Generate demo data for visualization
      generateDemoGraphData()
    } finally {
      setLoading(false)
    }
  }

  const generateDemoGraphData = () => {
    const demoData: GraphData = {
      nodes: [
        // Dormitory rooms
        { id: 'D001', type: 'Room', subtype: 'dormitory', properties: { side: 'sunny', faces_sun: true, building_side: 'east' }, x: 1, y: 3 },
        { id: 'D002', type: 'Room', subtype: 'dormitory', properties: { side: 'sunny', faces_sun: true, building_side: 'east' }, x: 1, y: 2 },
        { id: 'D003', type: 'Room', subtype: 'dormitory', properties: { side: 'sunny', faces_sun: true, building_side: 'east' }, x: 1, y: 1 },
        { id: 'D004', type: 'Room', subtype: 'dormitory', properties: { side: 'shaded', faces_sun: false, building_side: 'west' }, x: 5, y: 3 },
        { id: 'D005', type: 'Room', subtype: 'dormitory', properties: { side: 'shaded', faces_sun: false, building_side: 'west' }, x: 5, y: 2 },
        { id: 'D006', type: 'Room', subtype: 'dormitory', properties: { side: 'shaded', faces_sun: false, building_side: 'west' }, x: 5, y: 1 },
        // Mechanical rooms
        { id: 'M001', type: 'Room', subtype: 'mechanical', properties: { building_side: 'center' }, x: 2.5, y: 4 },
        { id: 'M002', type: 'Room', subtype: 'mechanical', properties: { building_side: 'center' }, x: 3.5, y: 4 },
        // AC Units
        { id: 'AC001', type: 'ACUnit', properties: { location: 'M001' }, x: 2.5, y: 4.5 },
        { id: 'AC002', type: 'ACUnit', properties: { location: 'M002' }, x: 3.5, y: 4.5 },
        // Temperature sensors
        { id: 'T001', type: 'Sensor', subtype: 'temperature', properties: { room: 'D001' }, x: 0.7, y: 3 },
        { id: 'T002', type: 'Sensor', subtype: 'temperature', properties: { room: 'D002' }, x: 0.7, y: 2 },
        { id: 'T003', type: 'Sensor', subtype: 'temperature', properties: { room: 'D003' }, x: 0.7, y: 1 },
        { id: 'T004', type: 'Sensor', subtype: 'temperature', properties: { room: 'D004' }, x: 5.3, y: 3 },
        { id: 'T005', type: 'Sensor', subtype: 'temperature', properties: { room: 'D005' }, x: 5.3, y: 2 },
        { id: 'T006', type: 'Sensor', subtype: 'temperature', properties: { room: 'D006' }, x: 5.3, y: 1 },
        // Occupancy sensors
        { id: 'O001', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D001' }, x: 1.3, y: 3 },
        { id: 'O002', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D002' }, x: 1.3, y: 2 },
        { id: 'O003', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D003' }, x: 1.3, y: 1 },
        { id: 'O004', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D004' }, x: 4.7, y: 3 },
        { id: 'O005', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D005' }, x: 4.7, y: 2 },
        { id: 'O006', type: 'Sensor', subtype: 'occupancy', properties: { room: 'D006' }, x: 4.7, y: 1 },
      ],
      edges: [
        // AC services relationships
        { source: 'AC001', target: 'D001', relationship: 'SERVICES' },
        { source: 'AC001', target: 'D002', relationship: 'SERVICES' },
        { source: 'AC001', target: 'D003', relationship: 'SERVICES' },
        { source: 'AC002', target: 'D004', relationship: 'SERVICES' },
        { source: 'AC002', target: 'D005', relationship: 'SERVICES' },
        { source: 'AC002', target: 'D006', relationship: 'SERVICES' },
        // Sensor relationships
        { source: 'D001', target: 'T001', relationship: 'HAS_SENSOR' },
        { source: 'D001', target: 'O001', relationship: 'HAS_SENSOR' },
        { source: 'D002', target: 'T002', relationship: 'HAS_SENSOR' },
        { source: 'D002', target: 'O002', relationship: 'HAS_SENSOR' },
        { source: 'D003', target: 'T003', relationship: 'HAS_SENSOR' },
        { source: 'D003', target: 'O003', relationship: 'HAS_SENSOR' },
        { source: 'D004', target: 'T004', relationship: 'HAS_SENSOR' },
        { source: 'D004', target: 'O004', relationship: 'HAS_SENSOR' },
        { source: 'D005', target: 'T005', relationship: 'HAS_SENSOR' },
        { source: 'D005', target: 'O005', relationship: 'HAS_SENSOR' },
        { source: 'D006', target: 'T006', relationship: 'HAS_SENSOR' },
        { source: 'D006', target: 'O006', relationship: 'HAS_SENSOR' },
        // Sensor reporting relationships
        { source: 'T001', target: 'AC001', relationship: 'REPORTS_TO' },
        { source: 'T002', target: 'AC001', relationship: 'REPORTS_TO' },
        { source: 'T003', target: 'AC001', relationship: 'REPORTS_TO' },
        { source: 'T004', target: 'AC002', relationship: 'REPORTS_TO' },
        { source: 'T005', target: 'AC002', relationship: 'REPORTS_TO' },
        { source: 'T006', target: 'AC002', relationship: 'REPORTS_TO' },
      ]
    }
    setGraphData(demoData)
    generatePlotData(demoData)
  }

  const generatePlotData = (data: GraphData) => {
    // Create node traces by type
    const roomNodes = data.nodes.filter(n => n.type === 'Room')
    const acNodes = data.nodes.filter(n => n.type === 'ACUnit')
    const tempSensorNodes = data.nodes.filter(n => n.type === 'Sensor' && n.subtype === 'temperature')
    const occSensorNodes = data.nodes.filter(n => n.type === 'Sensor' && n.subtype === 'occupancy')

    // Create edge traces
    const edgeTraces: any[] = []
    data.edges.forEach(edge => {
      const sourceNode = data.nodes.find(n => n.id === edge.source)
      const targetNode = data.nodes.find(n => n.id === edge.target)
      if (sourceNode && targetNode) {
        edgeTraces.push({
          x: [sourceNode.x, targetNode.x, null],
          y: [sourceNode.y, targetNode.y, null],
          mode: 'lines',
          line: {
            width: edge.relationship === 'SERVICES' ? 3 : 1,
            color: edge.relationship === 'SERVICES' ? '#3b82f6' : 
                   edge.relationship === 'HAS_SENSOR' ? '#10b981' : '#6b7280'
          },
          hoverinfo: 'none',
          showlegend: false
        })
      }
    })

    const traces = [
      ...edgeTraces,
      // Sunny side rooms
      {
        x: roomNodes.filter(n => n.properties.faces_sun).map(n => n.x),
        y: roomNodes.filter(n => n.properties.faces_sun).map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'Sunny Rooms',
        text: roomNodes.filter(n => n.properties.faces_sun).map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 40,
          color: '#fbbf24',
          symbol: 'square',
          line: { width: 2, color: '#f59e0b' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Dormitory Room<br>Side: Sunny (East)<br>Faces Sun: Yes<extra></extra>'
      },
      // Shaded side rooms
      {
        x: roomNodes.filter(n => !n.properties.faces_sun && n.subtype === 'dormitory').map(n => n.x),
        y: roomNodes.filter(n => !n.properties.faces_sun && n.subtype === 'dormitory').map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'Shaded Rooms',
        text: roomNodes.filter(n => !n.properties.faces_sun && n.subtype === 'dormitory').map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 40,
          color: '#6b7280',
          symbol: 'square',
          line: { width: 2, color: '#4b5563' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Dormitory Room<br>Side: Shaded (West)<br>Faces Sun: No<extra></extra>'
      },
      // Mechanical rooms
      {
        x: roomNodes.filter(n => n.subtype === 'mechanical').map(n => n.x),
        y: roomNodes.filter(n => n.subtype === 'mechanical').map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'Mechanical Rooms',
        text: roomNodes.filter(n => n.subtype === 'mechanical').map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 35,
          color: '#8b5cf6',
          symbol: 'square',
          line: { width: 2, color: '#7c3aed' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Mechanical Room<br>Location: Center<extra></extra>'
      },
      // AC Units
      {
        x: acNodes.map(n => n.x),
        y: acNodes.map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'AC Units',
        text: acNodes.map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 30,
          color: '#3b82f6',
          symbol: 'diamond',
          line: { width: 2, color: '#2563eb' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Air Conditioning Unit<br>Services: 3 rooms<extra></extra>'
      },
      // Temperature sensors
      {
        x: tempSensorNodes.map(n => n.x),
        y: tempSensorNodes.map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'Temperature Sensors',
        text: tempSensorNodes.map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 20,
          color: '#ef4444',
          symbol: 'circle',
          line: { width: 1, color: '#dc2626' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Temperature Sensor<br>Measurement: °C<extra></extra>'
      },
      // Occupancy sensors
      {
        x: occSensorNodes.map(n => n.x),
        y: occSensorNodes.map(n => n.y),
        mode: 'markers+text',
        type: 'scatter',
        name: 'Occupancy Sensors',
        text: occSensorNodes.map(n => n.id),
        textposition: 'middle center',
        marker: {
          size: 20,
          color: '#10b981',
          symbol: 'circle',
          line: { width: 1, color: '#059669' }
        },
        hovertemplate: '<b>%{text}</b><br>Type: Occupancy Sensor<br>Measurement: 0/1<extra></extra>'
      }
    ]

    setPlotData(traces)
    setLayout({
      title: {
        text: 'DormIQ Building Structure - Neo4j Graph Database',
        font: { size: 18, color: '#1f2937' }
      },
      xaxis: {
        showgrid: false,
        zeroline: false,
        showticklabels: false,
        range: [0, 6]
      },
      yaxis: {
        showgrid: false,
        zeroline: false,
        showticklabels: false,
        range: [0, 5]
      },
      showlegend: true,
      legend: {
        x: 1.02,
        y: 1,
        bgcolor: 'rgba(255,255,255,0.8)',
        bordercolor: '#e5e7eb',
        borderwidth: 1
      },
      plot_bgcolor: '#f9fafb',
      paper_bgcolor: 'white',
      margin: { l: 50, r: 150, t: 80, b: 50 },
      annotations: [
        {
          x: 0.5,
          y: 4.7,
          text: 'East Side (Sunny)',
          showarrow: false,
          font: { size: 14, color: '#f59e0b' }
        },
        {
          x: 5.5,
          y: 4.7,
          text: 'West Side (Shaded)',
          showarrow: false,
          font: { size: 14, color: '#6b7280' }
        }
      ]
    })
  }

  useEffect(() => {
    fetchGraphData()
  }, [])

  if (loading) {
    return (
      <Card className="w-full h-[600px]">
        <CardHeader>
          <CardTitle>Neo4j Graph Visualization</CardTitle>
          <CardDescription>Loading building structure...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[500px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Neo4j Graph Visualization</CardTitle>
            <CardDescription>
              Interactive view of dormitory building structure, AC units, and sensors
            </CardDescription>
          </div>
          <Button onClick={fetchGraphData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              Using demo data: {error}
            </p>
          </div>
        )}
        <div className="h-[600px]">
          <Plot
            data={plotData}
            layout={layout}
            config={{
              displayModeBar: true,
              modeBarButtonsToRemove: ['pan2d', 'select2d', 'lasso2d', 'autoScale2d'],
              displaylogo: false,
              responsive: true
            }}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      </CardContent>
    </Card>
  )
}
