#!/usr/bin/env python3
"""
DormIQ Analytics Backend Startup Script
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        logger.error("Python 3.9 or higher is required")
        sys.exit(1)
    logger.info(f"Python version: {sys.version}")

def check_env_file():
    """Check if .env file exists"""
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning(".env file not found. Please copy .env.example to .env and configure it.")
        return False
    logger.info(".env file found")
    return True

def install_dependencies():
    """Install Python dependencies"""
    logger.info("Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True)
        logger.info("Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        logger.error(f"Error output: {e.stderr}")
        sys.exit(1)

def start_server():
    """Start the FastAPI server"""
    logger.info("Starting DormIQ Analytics Backend...")
    logger.info("Server will be available at: http://localhost:8000")
    logger.info("API documentation will be available at: http://localhost:8000/docs")
    logger.info("Press Ctrl+C to stop the server")
    
    try:
        # Import and run the main application
        import uvicorn
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    logger.info("=== DormIQ Analytics Backend Startup ===")
    
    # Check Python version
    check_python_version()
    
    # Check environment file
    if not check_env_file():
        logger.info("Please configure your .env file before starting the server")
        sys.exit(1)
    
    # Install dependencies
    install_dependencies()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
