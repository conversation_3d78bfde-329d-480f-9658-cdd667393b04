"""
Chat API routes for natural language querying
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.core.llm import llm_manager

logger = logging.getLogger(__name__)

router = APIRouter()


class ChatRequest(BaseModel):
    """Chat request model"""
    message: str
    conversation_id: str = None


class QueryFlowStep(BaseModel):
    """Query flow step model"""
    step: str
    description: str
    timestamp: str


class QueryFlow(BaseModel):
    """Query flow tracking model"""
    query: str
    databases_queried: List[str] = []
    queries_executed: List[str] = []
    data_sources_used: List[str] = []
    processing_steps: List[QueryFlowStep] = []
    execution_time: float = 0
    success: bool = False
    error: Optional[str] = None


class ChatResponse(BaseModel):
    """Chat response model"""
    id: str
    role: str
    content: str
    timestamp: datetime
    query_type: Optional[str] = None
    query_flow: Optional[QueryFlow] = None
    error: Optional[str] = None


@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """
    Main chat endpoint for natural language queries

    This endpoint processes user queries using the LLM agent which can:
    - Route queries to appropriate databases (Neo4j/InfluxDB)
    - Generate database queries from natural language
    - Synthesize responses from multiple data sources
    """
    try:
        logger.info(f"Processing chat request: {request.message}")

        # Process the query using LLM manager
        result = await llm_manager.process_query(request.message)

        # Create response
        response_data = {
            "id": str(int(datetime.now().timestamp() * 1000)),
            "role": "assistant",
            "content": result["response"],
            "timestamp": datetime.now(),
        }

        if result.get("query_type"):
            response_data["query_type"] = result["query_type"]
        if result.get("error"):
            response_data["error"] = result["error"]
        if result.get("query_flow"):
            # Convert query_flow to proper format
            query_flow_data = result["query_flow"]
            if isinstance(query_flow_data.get("start_time"), datetime):
                query_flow_data.pop("start_time", None)  # Remove datetime object
            response_data["query_flow"] = QueryFlow(**query_flow_data)

        response = ChatResponse(**response_data)

        logger.info(f"Chat response generated successfully")
        return response

    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")

        # Return error response
        error_response = ChatResponse(
            id=str(int(datetime.now().timestamp() * 1000)),
            role="assistant",
            content="I apologize, but I'm having trouble processing your request right now. Please make sure the system is initialized and try again.",
            timestamp=datetime.now(),
            error=str(e)
        )

        return error_response


@router.get("/chat/health")
async def chat_health():
    """Check chat service health"""
    try:
        health_status = await llm_manager.health_check()
        return {
            "service": "chat",
            "status": health_status["status"],
            "llm_model": "gemini-1.5-flash",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Chat health check failed: {e}")
        raise HTTPException(status_code=503, detail="Chat service unavailable")


# Visualization data endpoint moved to visualization.py router
