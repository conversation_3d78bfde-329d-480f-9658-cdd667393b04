"use client"

import { useState } from "react"
import { ChatInterface } from "@/components/chat-interface"
import { ControlSidebar } from "@/components/control-sidebar"
import { VisualizationPanel } from "@/components/visualization-panel"
import { SidebarProvider } from "@/components/ui/sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"

export default function Home() {
  const [showQueryFlow, setShowQueryFlow] = useState(false)
  const [isSystemInitialized, setIsSystemInitialized] = useState(false)
  const [activeTab, setActiveTab] = useState("chat")
  const [isLoading, setIsLoading] = useState(false)
  const [messages, setMessages] = useState([
    {
      id: "1",
      role: "assistant" as const,
      content:
        "Welcome to DormIQ Analytics. I can help you query your graph and time series databases with natural language. Please initialize the system to begin.",
      timestamp: new Date(),
    },
  ])

  const handleInitializeSystem = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/api/initialize`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const result = await response.json()

      if (result.success) {
        setIsSystemInitialized(true)
        const newMessage = {
          id: Date.now().toString(),
          role: "system" as const,
          content: result.message,
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, newMessage])
      } else {
        const errorMessage = {
          id: Date.now().toString(),
          role: "system" as const,
          content: `Initialization failed: ${result.message}`,
          timestamp: new Date(),
        }
        setMessages((prev) => [...prev, errorMessage])
      }
    } catch (error) {
      console.error("Initialization error:", error)
      const errorMessage = {
        id: Date.now().toString(),
        role: "system" as const,
        content: "Failed to connect to backend. Please ensure the backend server is running on port 8000.",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendMessage = async (content: string) => {
    const userMessage = {
      id: Date.now().toString(),
      role: "user" as const,
      content,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])

    try {
      const response = await fetch(`${API_BASE_URL}/api/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: content,
          conversation_id: "default"
        }),
      })

      const result = await response.json()

      const aiMessage = {
        id: result.id || (Date.now() + 1).toString(),
        role: "assistant" as const,
        content: result.content || "I apologize, but I couldn't process your request at the moment.",
        timestamp: new Date(result.timestamp) || new Date(),
        query_flow: result.query_flow || undefined,
      }
      setMessages((prev) => [...prev, aiMessage])
    } catch (error) {
      console.error("Chat error:", error)
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant" as const,
        content: "I'm sorry, but I'm having trouble connecting to the backend. Please check if the server is running.",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    }
  }

  const handleClearChat = () => {
    setMessages([])
  }

  return (
    <div className="min-h-screen bg-gradient-black-blue">
      <SidebarProvider defaultOpen={true}>
        <div className="flex h-screen">
          <ControlSidebar
            showQueryFlow={showQueryFlow}
            setShowQueryFlow={setShowQueryFlow}
            isSystemInitialized={isSystemInitialized}
            onInitializeSystem={handleInitializeSystem}
            isLoading={isLoading}
          />
          <main className="flex-1 flex flex-col">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <div className="border-b border-border bg-black/50 backdrop-blur-sm p-4">
                <TabsList className="grid w-fit grid-cols-2 bg-black/60 border border-custom-blue/30">
                  <TabsTrigger
                    value="chat"
                    className="data-[state=active]:bg-custom-blue data-[state=active]:text-white"
                  >
                    Chat Assistant
                  </TabsTrigger>
                  <TabsTrigger
                    value="visualization"
                    className="data-[state=active]:bg-custom-blue data-[state=active]:text-white"
                  >
                    Data Visualization
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="chat" className="flex-1 m-0">
                <ChatInterface
                  messages={messages}
                  onSendMessage={handleSendMessage}
                  onClearChat={handleClearChat}
                  showQueryFlow={showQueryFlow}
                  isSystemInitialized={isSystemInitialized}
                />
              </TabsContent>

              <TabsContent value="visualization" className="flex-1 m-0">
                <VisualizationPanel isSystemInitialized={isSystemInitialized} />
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </SidebarProvider>
    </div>
  )
}
