"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Clock,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-flow-panel */ \"(app-pages-browser)/./components/query-flow-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, onSendMessage, onClearChat, showQueryFlow, isSystemInitialized } = param;\n    _s();\n    const [selectedQueryFlow, setSelectedQueryFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (scrollAreaRef.current) {\n                scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (input.trim() && isSystemInitialized) {\n            onSendMessage(input.trim());\n            setInput(\"\");\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-custom-blue/20 bg-black/50 backdrop-blur-sm p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: \"Endeavour AI Assitant\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-custom-blue-light\",\n                                    children: \"Query your dormitory data with natural language\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"gap-1 border-custom-blue/50 text-custom-blue-light\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Graph DB\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"gap-1 border-custom-blue/50 text-custom-blue-light\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Time Series\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onClearChat,\n                                    className: \"text-gray-400 hover:text-white hover:bg-red-500/20 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Clear\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                ref: scrollAreaRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(message.role === \"system\" ? \"bg-amber-500/20 text-amber-400 border border-amber-500/50\" : \"bg-custom-blue/20 text-custom-blue-light border border-custom-blue/50\"),\n                                    children: message.role === \"system\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 48\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 83\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"max-w-[70%] p-4 \".concat(message.role === \"user\" ? \"bg-custom-blue text-white border-custom-blue/50 shadow-lg\" : message.role === \"system\" ? \"bg-amber-500/10 border-amber-500/30 text-white\" : \"bg-black/40 border-custom-blue/20 text-white\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-3 bg-black/20 rounded-lg border border-custom-blue/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-custom-blue-light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-custom-blue-light\",\n                                                                children: \"Query Execution Flow\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 ml-auto\",\n                                                                children: [\n                                                                    message.query_flow.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-green-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 144,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Success\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-red-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 149,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Failed\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400 ml-2\",\n                                                                        children: [\n                                                                            message.query_flow.execution_time.toFixed(2),\n                                                                            \"s\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: message.query_flow.databases_queried.length > 0 ? [\n                                                            ...new Set(message.query_flow.databases_queried)\n                                                        ].map((db, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs px-2 py-0 h-5 border-custom-blue/30 text-custom-blue-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    db\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 29\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs px-2 py-0 h-5 border-purple-500/30 text-purple-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"LLM Knowledge\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            message.query_flow.processing_steps.length,\n                                                            \" steps: \",\n                                                            message.query_flow.processing_steps.map((step)=>step.step.replace('_', ' ')).join(' → ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs \".concat(message.role === \"user\" ? \"text-blue-200\" : \"text-gray-400\"),\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedQueryFlow(message.query_flow),\n                                                        className: \"h-6 px-2 text-xs text-custom-blue-light hover:text-custom-blue hover:bg-custom-blue/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-black/40 rounded-full flex items-center justify-center border border-custom-blue/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 text-custom-blue-light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    placeholder: isSystemInitialized ? \"Ask me about your dormitory data...\" : \"Please initialize the system first\",\n                                    disabled: !isSystemInitialized,\n                                    className: \"flex-1 bg-black/40 border-custom-blue/30 text-white placeholder:text-gray-400 focus:border-custom-blue focus:ring-custom-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: !input.trim() || !isSystemInitialized,\n                                    className: \"bg-custom-blue hover:bg-custom-blue-dark px-6 border border-custom-blue/50 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Clock_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        !isSystemInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-amber-400 mt-2\",\n                            children: \"Initialize the system from the control panel to start querying\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            selectedQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/30 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Query Execution Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSelectedQueryFlow(null),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__.QueryFlowPanel, {\n                            queryFlow: selectedQueryFlow,\n                            isVisible: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"OMVXCnMtObGtahIhyRKMJLAwMek=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});