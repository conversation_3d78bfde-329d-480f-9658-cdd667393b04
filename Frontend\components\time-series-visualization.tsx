'use client'

import React, { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, RefreshCw, TrendingUp, Thermometer, Users } from "lucide-react"

// Dynamically import Plotly to avoid SSR issues
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false })

interface TimeSeriesData {
  timestamp: string
  value: number
  room: string
  measurement: string
  side?: string
  profile_type?: string
}

interface ChartConfig {
  title: string
  yAxisTitle: string
  colorScheme: string[]
}

export default function TimeSeriesVisualization() {
  const [temperatureData, setTemperatureData] = useState<TimeSeriesData[]>([])
  const [occupancyData, setOccupancyData] = useState<TimeSeriesData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('24h')
  const [selectedRooms, setSelectedRooms] = useState<string[]>(['all'])

  const fetchTimeSeriesData = async () => {
    setLoading(true)
    setError(null)
    try {
      const [tempResponse, occResponse] = await Promise.all([
        fetch(`/api/visualization/temperature-data?range=${timeRange}&rooms=${selectedRooms.join(',')}`),
        fetch(`/api/visualization/occupancy-data?range=${timeRange}&rooms=${selectedRooms.join(',')}`)
      ])

      if (!tempResponse.ok || !occResponse.ok) {
        throw new Error('Failed to fetch time series data')
      }

      const tempData = await tempResponse.json()
      const occData = await occResponse.json()
      
      setTemperatureData(tempData)
      setOccupancyData(occData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      // Generate demo data for visualization
      generateDemoData()
    } finally {
      setLoading(false)
    }
  }

  const generateDemoData = () => {
    const now = new Date()
    const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 720
    const interval = timeRange === '24h' ? 1 : timeRange === '7d' ? 1 : 24

    const tempData: TimeSeriesData[] = []
    const occData: TimeSeriesData[] = []

    const rooms = ['D001', 'D002', 'D003', 'D004', 'D005', 'D006']
    const roomProfiles = {
      'D001': 'full-time', 'D002': 'working-night', 'D003': 'full-time',
      'D004': 'working-night', 'D005': 'full-time', 'D006': 'working-night'
    }

    for (let i = 0; i < hours; i += interval) {
      const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000)
      const hour = timestamp.getHours()
      
      rooms.forEach(room => {
        const isSunny = ['D001', 'D002', 'D003'].includes(room)
        const side = isSunny ? 'sunny' : 'shaded'
        
        // Temperature data with realistic patterns
        let baseTemp = isSunny ? 24.5 : 22.0
        const dailyVariation = Math.sin((hour - 6) * Math.PI / 12) * 2
        const randomNoise = (Math.random() - 0.5) * 1.5
        const temperature = baseTemp + dailyVariation + randomNoise
        
        tempData.push({
          timestamp: timestamp.toISOString(),
          value: Math.round(temperature * 10) / 10,
          room,
          measurement: 'temperature',
          side
        })

        // Occupancy data with profile-based patterns
        const profile = roomProfiles[room as keyof typeof roomProfiles]
        let occupied = 0
        
        if (profile === 'full-time') {
          occupied = (hour >= 7 && hour <= 9) || (hour >= 13 && hour <= 15) || 
                    (hour >= 20 && hour <= 22) || (hour >= 23 || hour <= 6) ? 1 : 0
        } else {
          occupied = (hour >= 6 && hour <= 8) || (hour >= 16 && hour <= 18) || 
                    (hour >= 21 && hour <= 23) || (hour <= 5) ? 1 : 0
        }
        
        // Add some randomness
        if (Math.random() < 0.2) occupied = 1 - occupied
        
        occData.push({
          timestamp: timestamp.toISOString(),
          value: occupied,
          room,
          measurement: 'occupancy',
          profile_type: profile
        })
      })
    }

    setTemperatureData(tempData.reverse())
    setOccupancyData(occData.reverse())
  }

  const createTemperaturePlot = () => {
    const sunnyRooms = ['D001', 'D002', 'D003']
    const shadedRooms = ['D004', 'D005', 'D006']

    const traces = []

    // Sunny side rooms
    sunnyRooms.forEach((room, index) => {
      const roomData = temperatureData.filter(d => d.room === room)
      traces.push({
        x: roomData.map(d => d.timestamp),
        y: roomData.map(d => d.value),
        type: 'scatter',
        mode: 'lines',
        name: `${room} (Sunny)`,
        line: {
          color: ['#fbbf24', '#f59e0b', '#d97706'][index],
          width: 2
        },
        hovertemplate: '<b>%{fullData.name}</b><br>Time: %{x}<br>Temperature: %{y}°C<extra></extra>'
      })
    })

    // Shaded side rooms
    shadedRooms.forEach((room, index) => {
      const roomData = temperatureData.filter(d => d.room === room)
      traces.push({
        x: roomData.map(d => d.timestamp),
        y: roomData.map(d => d.value),
        type: 'scatter',
        mode: 'lines',
        name: `${room} (Shaded)`,
        line: {
          color: ['#6b7280', '#4b5563', '#374151'][index],
          width: 2
        },
        hovertemplate: '<b>%{fullData.name}</b><br>Time: %{x}<br>Temperature: %{y}°C<extra></extra>'
      })
    })

    return {
      data: traces,
      layout: {
        title: 'Temperature Trends by Room',
        xaxis: { title: 'Time' },
        yaxis: { title: 'Temperature (°C)' },
        legend: { x: 1.02, y: 1 },
        margin: { r: 150 },
        hovermode: 'x unified'
      }
    }
  }

  const createOccupancyPlot = () => {
    const rooms = ['D001', 'D002', 'D003', 'D004', 'D005', 'D006']
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

    const traces = rooms.map((room, index) => {
      const roomData = occupancyData.filter(d => d.room === room)
      const profile = roomData[0]?.profile_type || 'unknown'
      
      return {
        x: roomData.map(d => d.timestamp),
        y: roomData.map(d => d.value),
        type: 'scatter',
        mode: 'lines',
        name: `${room} (${profile})`,
        line: {
          color: colors[index],
          width: 2,
          shape: 'hv'
        },
        hovertemplate: '<b>%{fullData.name}</b><br>Time: %{x}<br>Occupied: %{y}<extra></extra>'
      }
    })

    return {
      data: traces,
      layout: {
        title: 'Occupancy Patterns by Room',
        xaxis: { title: 'Time' },
        yaxis: { title: 'Occupancy (0=Empty, 1=Occupied)', range: [-0.1, 1.1] },
        legend: { x: 1.02, y: 1 },
        margin: { r: 150 },
        hovermode: 'x unified'
      }
    }
  }

  const createComparisonPlot = () => {
    // Average temperature by side
    const sunnyAvg = temperatureData
      .filter(d => ['D001', 'D002', 'D003'].includes(d.room))
      .reduce((acc, curr, _, arr) => {
        const timeGroup = curr.timestamp.substring(0, 13) // Group by hour
        if (!acc[timeGroup]) acc[timeGroup] = []
        acc[timeGroup].push(curr.value)
        return acc
      }, {} as Record<string, number[]>)

    const shadedAvg = temperatureData
      .filter(d => ['D004', 'D005', 'D006'].includes(d.room))
      .reduce((acc, curr, _, arr) => {
        const timeGroup = curr.timestamp.substring(0, 13)
        if (!acc[timeGroup]) acc[timeGroup] = []
        acc[timeGroup].push(curr.value)
        return acc
      }, {} as Record<string, number[]>)

    const sunnyTrace = {
      x: Object.keys(sunnyAvg).map(time => new Date(time + ':00:00')),
      y: Object.values(sunnyAvg).map(values => values.reduce((a, b) => a + b, 0) / values.length),
      type: 'scatter',
      mode: 'lines+markers',
      name: 'Sunny Side Average',
      line: { color: '#fbbf24', width: 3 },
      marker: { size: 6 }
    }

    const shadedTrace = {
      x: Object.keys(shadedAvg).map(time => new Date(time + ':00:00')),
      y: Object.values(shadedAvg).map(values => values.reduce((a, b) => a + b, 0) / values.length),
      type: 'scatter',
      mode: 'lines+markers',
      name: 'Shaded Side Average',
      line: { color: '#6b7280', width: 3 },
      marker: { size: 6 }
    }

    return {
      data: [sunnyTrace, shadedTrace],
      layout: {
        title: 'Temperature Comparison: Sunny vs Shaded Sides',
        xaxis: { title: 'Time' },
        yaxis: { title: 'Average Temperature (°C)' },
        legend: { x: 0.02, y: 0.98 },
        hovermode: 'x unified'
      }
    }
  }

  useEffect(() => {
    fetchTimeSeriesData()
  }, [timeRange, selectedRooms])

  if (loading) {
    return (
      <Card className="w-full h-[600px]">
        <CardHeader>
          <CardTitle>Time Series Visualization</CardTitle>
          <CardDescription>Loading sensor data...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[500px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>InfluxDB Time Series Visualization</CardTitle>
            <CardDescription>
              Real-time temperature and occupancy data from dormitory sensors
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="24h">Last 24h</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={fetchTimeSeriesData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              Using demo data: {error}
            </p>
          </div>
        )}
        
        <Tabs defaultValue="temperature" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="temperature" className="flex items-center gap-2">
              <Thermometer className="h-4 w-4" />
              Temperature
            </TabsTrigger>
            <TabsTrigger value="occupancy" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Occupancy
            </TabsTrigger>
            <TabsTrigger value="comparison" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Comparison
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="temperature" className="mt-6">
            <div className="h-[500px]">
              <Plot
                {...createTemperaturePlot()}
                config={{
                  displayModeBar: true,
                  displaylogo: false,
                  responsive: true
                }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="occupancy" className="mt-6">
            <div className="h-[500px]">
              <Plot
                {...createOccupancyPlot()}
                config={{
                  displayModeBar: true,
                  displaylogo: false,
                  responsive: true
                }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="comparison" className="mt-6">
            <div className="h-[500px]">
              <Plot
                {...createComparisonPlot()}
                config={{
                  displayModeBar: true,
                  displaylogo: false,
                  responsive: true
                }}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
