"""
System API routes for initialization and status
"""

import logging
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.core.database import neo4j_manager, influxdb_manager
from app.core.llm import llm_manager
from app.services.data_generator import DataGenerator

logger = logging.getLogger(__name__)

router = APIRouter()


class InitializeResponse(BaseModel):
    """System initialization response"""
    success: bool
    message: str
    timestamp: datetime
    services_initialized: Dict[str, bool]


class StatusResponse(BaseModel):
    """System status response"""
    system_initialized: bool
    services: Dict[str, Dict[str, Any]]
    timestamp: datetime


@router.post("/initialize", response_model=InitializeResponse)
async def initialize_system():
    """
    Initialize the DormIQ system

    This endpoint:
    1. Checks database connections
    2. Generates sample data for both Neo4j and InfluxDB
    3. Verifies LLM connectivity
    4. Returns initialization status
    """
    try:
        logger.info("Starting system initialization...")

        services_status = {
            "neo4j": False,
            "influxdb": False,
            "llm": False,
            "data_generation": False
        }

        # Check Neo4j connection
        neo4j_health = await neo4j_manager.health_check()
        if neo4j_health["status"] == "connected":
            services_status["neo4j"] = True
            logger.info("Neo4j connection verified")
        else:
            logger.error(f"Neo4j connection failed: {neo4j_health}")

        # Check InfluxDB connection
        influxdb_health = await influxdb_manager.health_check()
        if influxdb_health["status"] == "connected":
            services_status["influxdb"] = True
            logger.info("InfluxDB connection verified")
        else:
            logger.error(f"InfluxDB connection failed: {influxdb_health}")

        # Check LLM
        llm_health = await llm_manager.health_check()
        if llm_health["status"] == "connected":
            services_status["llm"] = True
            logger.info("LLM connection verified")
        else:
            logger.error(f"LLM connection failed: {llm_health}")

        # Generate sample data if all services are connected
        if all([services_status["neo4j"], services_status["influxdb"]]):
            try:
                data_generator = DataGenerator()
                # Clear existing data first to ensure fresh start
                await data_generator.clear_all_data()
                # Generate new data
                await data_generator.generate_all_data()
                services_status["data_generation"] = True
                logger.info("Sample data generated successfully")
            except Exception as e:
                logger.error(f"Data generation failed: {e}")

        # Determine overall success
        success = all(services_status.values())

        if success:
            message = "System initialized successfully. Graph DB and Time Series DB connections established. Ready to process queries."
        else:
            failed_services = [service for service, status in services_status.items() if not status]
            message = f"System initialization partially failed. Issues with: {', '.join(failed_services)}"

        response = InitializeResponse(
            success=success,
            message=message,
            timestamp=datetime.now(),
            services_initialized=services_status
        )

        logger.info(f"System initialization completed: {success}")
        return response

    except Exception as e:
        logger.error(f"System initialization error: {e}")

        error_response = InitializeResponse(
            success=False,
            message=f"System initialization failed: {str(e)}",
            timestamp=datetime.now(),
            services_initialized={
                "neo4j": False,
                "influxdb": False,
                "llm": False,
                "data_generation": False
            }
        )

        return error_response


@router.get("/status", response_model=StatusResponse)
async def get_system_status():
    """
    Get current system status

    Returns the health status of all system components
    """
    try:
        # Check all services
        neo4j_health = await neo4j_manager.health_check()
        influxdb_health = await influxdb_manager.health_check()
        llm_health = await llm_manager.health_check()

        services = {
            "neo4j": neo4j_health,
            "influxdb": influxdb_health,
            "llm": llm_health
        }

        # Determine if system is fully initialized
        system_initialized = all(
            service["status"] == "connected"
            for service in services.values()
        )

        response = StatusResponse(
            system_initialized=system_initialized,
            services=services,
            timestamp=datetime.now()
        )

        return response

    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(status_code=503, detail="Unable to check system status")


@router.post("/reset")
async def reset_system():
    """
    Reset the system (clear data and reinitialize)
    """
    try:
        logger.info("Resetting system...")

        # Clear data from databases
        data_generator = DataGenerator()
        await data_generator.clear_all_data()

        # Reinitialize
        return await initialize_system()

    except Exception as e:
        logger.error(f"System reset error: {e}")
        raise HTTPException(status_code=500, detail=f"System reset failed: {str(e)}")
