"use client"

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Database, Settings, Activity, CheckCircle, Brain, ArrowRight, Search, Zap, BarChart3, Loader2 } from "lucide-react"

interface ControlSidebarProps {
  showQueryFlow: boolean
  setShowQueryFlow: (show: boolean) => void
  isSystemInitialized: boolean
  onInitializeSystem: () => void
  isLoading?: boolean
}

export function ControlSidebar({
  showQueryFlow,
  setShowQueryFlow,
  isSystemInitialized,
  onInitializeSystem,
  isLoading = false,
}: ControlSidebarProps) {
  return (
    <Sidebar className="border-r border-custom-blue/20 bg-black/80 backdrop-blur-sm">
      <SidebarHeader className="p-6 border-b border-custom-blue/20">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-blue-black rounded-xl flex items-center justify-center shadow-lg border border-custom-blue/30">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="font-bold text-white text-xl">Endeavour</h2>
            <p className="text-xs text-custom-blue-light">Intelligent AI Assistant </p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-4">
        <SidebarGroup>
          <SidebarGroupLabel className="text-white font-medium text-sm uppercase tracking-wider">
            System Control
          </SidebarGroupLabel>
          <SidebarGroupContent className="space-y-4">
            <div className="space-y-3">
              <Button
                onClick={onInitializeSystem}
                disabled={isSystemInitialized || isLoading}
                className="w-full justify-start gap-2 bg-custom-blue hover:bg-custom-blue-dark text-white border border-custom-blue/50 shadow-lg"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : isSystemInitialized ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <Settings className="w-4 h-4" />
                )}
                {isLoading ? "Initializing..." : isSystemInitialized ? "System Ready" : "Initialize System"}
              </Button>

              <div className="flex items-center justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20">
                <Label htmlFor="query-flow" className="text-sm text-white">
                  Query Flow
                </Label>
                <Switch id="query-flow" checked={showQueryFlow} onCheckedChange={setShowQueryFlow} />
              </div>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-4 bg-custom-blue/20" />

        <SidebarGroup>
          <SidebarGroupLabel className="text-white font-medium text-sm uppercase tracking-wider">
            Database Status
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton className="w-full justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20 hover:bg-black/60">
                  <div className="flex items-center gap-2">
                    <Database className="w-4 h-4 text-custom-blue-light" />
                    <span className="text-sm text-white">Graph DB</span>
                  </div>
                  <Badge
                    variant={isSystemInitialized ? "default" : "secondary"}
                    className={
                      isSystemInitialized
                        ? "bg-custom-blue/20 text-custom-blue-light border-custom-blue/50"
                        : "bg-gray-800 text-gray-400"
                    }
                  >
                    {isSystemInitialized ? "Connected" : "Offline"}
                  </Badge>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton className="w-full justify-between p-3 bg-black/40 rounded-lg border border-custom-blue/20 hover:bg-black/60">
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-custom-blue-light" />
                    <span className="text-sm text-white">Time Series DB</span>
                  </div>
                  <Badge
                    variant={isSystemInitialized ? "default" : "secondary"}
                    className={
                      isSystemInitialized
                        ? "bg-custom-blue/20 text-custom-blue-light border-custom-blue/50"
                        : "bg-gray-800 text-gray-400"
                    }
                  >
                    {isSystemInitialized ? "Connected" : "Offline"}
                  </Badge>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        
      </SidebarContent>

      <SidebarFooter className="p-4 border-t border-custom-blue/20">
        <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
          <span>Powered by</span>
          <div className="flex items-center gap-1 font-semibold text-custom-blue-light">
            <div className="w-4 h-4 bg-custom-blue rounded flex items-center justify-center">
              <span className="text-white text-xs font-bold">E</span>
            </div>
            Endeavour
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
