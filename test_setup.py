#!/usr/bin/env python3
"""
DormIQ Analytics Backend Setup Test
Tests database connections and API key validity
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_neo4j_connection():
    """Test Neo4j connection"""
    logger.info("Testing Neo4j connection...")
    try:
        from app.core.database import neo4j_manager
        await neo4j_manager.connect()
        health = await neo4j_manager.health_check()
        if health["status"] == "connected":
            logger.info("✅ Neo4j connection successful")
            return True
        else:
            logger.error(f"❌ Neo4j connection failed: {health}")
            return False
    except Exception as e:
        logger.error(f"❌ Neo4j connection error: {e}")
        return False

async def test_influxdb_connection():
    """Test InfluxDB connection"""
    logger.info("Testing InfluxDB connection...")
    try:
        from app.core.database import influxdb_manager
        await influxdb_manager.connect()
        health = await influxdb_manager.health_check()
        if health["status"] == "connected":
            logger.info("✅ InfluxDB connection successful")
            return True
        else:
            logger.error(f"❌ InfluxDB connection failed: {health}")
            return False
    except Exception as e:
        logger.error(f"❌ InfluxDB connection error: {e}")
        return False

async def test_llm_connection():
    """Test LLM connection"""
    logger.info("Testing Google Gemini API connection...")
    try:
        from app.core.llm import llm_manager
        await llm_manager.initialize()
        health = await llm_manager.health_check()
        if health["status"] == "connected":
            logger.info("✅ Google Gemini API connection successful")
            return True
        else:
            logger.error(f"❌ Google Gemini API connection failed: {health}")
            return False
    except Exception as e:
        logger.error(f"❌ Google Gemini API connection error: {e}")
        return False

def check_environment_variables():
    """Check if all required environment variables are set"""
    logger.info("Checking environment variables...")
    
    required_vars = [
        "GOOGLE_API_KEY",
        "NEO4J_URI",
        "NEO4J_USERNAME", 
        "NEO4J_PASSWORD",
        "INFLUXDB_URL",
        "INFLUXDB_TOKEN",
        "INFLUXDB_ORG",
        "INFLUXDB_BUCKET"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file")
        return False
    else:
        logger.info("✅ All required environment variables are set")
        return True

async def main():
    """Main test function"""
    logger.info("=== DormIQ Analytics Backend Setup Test ===")
    
    # Check environment variables
    env_ok = check_environment_variables()
    if not env_ok:
        logger.error("Environment variable check failed. Please fix your .env file.")
        return
    
    # Test connections
    results = []
    
    # Test Neo4j
    neo4j_ok = await test_neo4j_connection()
    results.append(("Neo4j", neo4j_ok))
    
    # Test InfluxDB
    influxdb_ok = await test_influxdb_connection()
    results.append(("InfluxDB", influxdb_ok))
    
    # Test LLM
    llm_ok = await test_llm_connection()
    results.append(("Google Gemini API", llm_ok))
    
    # Summary
    logger.info("\n=== Test Results Summary ===")
    all_passed = True
    for service, status in results:
        status_icon = "✅" if status else "❌"
        logger.info(f"{status_icon} {service}: {'PASS' if status else 'FAIL'}")
        if not status:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 All tests passed! Your backend is ready to run.")
        logger.info("You can now start the server with: python main.py")
    else:
        logger.error("\n⚠️  Some tests failed. Please check your configuration.")
        logger.error("Make sure all services are running and credentials are correct.")

if __name__ == "__main__":
    asyncio.run(main())
