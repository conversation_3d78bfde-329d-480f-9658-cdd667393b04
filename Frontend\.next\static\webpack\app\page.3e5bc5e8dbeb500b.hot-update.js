"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bot,Database,Send,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/query-flow-panel */ \"(app-pages-browser)/./components/query-flow-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, onSendMessage, onClearChat, showQueryFlow, isSystemInitialized } = param;\n    _s();\n    const [selectedQueryFlow, setSelectedQueryFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (scrollAreaRef.current) {\n                scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (input.trim() && isSystemInitialized) {\n            onSendMessage(input.trim());\n            setInput(\"\");\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-black/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-custom-blue/20 bg-black/50 backdrop-blur-sm p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-white\",\n                                    children: \"DormIQ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-custom-blue-light\",\n                                    children: \"When your building speaks, we listen.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onClearChat,\n                                className: \"text-gray-400 hover:text-white hover:bg-red-500/20 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Clear\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1 p-4\",\n                ref: scrollAreaRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role !== \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(message.role === \"system\" ? \"bg-amber-500/20 text-amber-400 border border-amber-500/50\" : \"bg-custom-blue/20 text-custom-blue-light border border-custom-blue/50\"),\n                                    children: message.role === \"system\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 48\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 83\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"max-w-[70%] p-4 \".concat(message.role === \"user\" ? \"bg-custom-blue text-white border-custom-blue/50 shadow-lg\" : message.role === \"system\" ? \"bg-amber-500/10 border-amber-500/30 text-white\" : \"bg-black/40 border-custom-blue/20 text-white\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm leading-relaxed\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 p-3 bg-black/20 rounded-lg border border-custom-blue/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 text-custom-blue-light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-custom-blue-light\",\n                                                                children: \"Query Execution Flow\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 ml-auto\",\n                                                                children: [\n                                                                    message.query_flow.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-green-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 144,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Success\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-xs text-red-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                                lineNumber: 149,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Failed\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400 ml-2\",\n                                                                        children: [\n                                                                            message.query_flow.execution_time.toFixed(2),\n                                                                            \"s\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: message.query_flow.databases_queried.length > 0 ? [\n                                                            ...new Set(message.query_flow.databases_queried)\n                                                        ].map((db, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs px-2 py-0 h-5 border-custom-blue/30 text-custom-blue-light\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    db\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 29\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs px-2 py-0 h-5 border-purple-500/30 text-purple-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"LLM Knowledge\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            message.query_flow.processing_steps.length,\n                                                            \" steps: \",\n                                                            message.query_flow.processing_steps.map((step)=>step.step.replace('_', ' ')).join(' → ')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs \".concat(message.role === \"user\" ? \"text-blue-200\" : \"text-gray-400\"),\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    message.query_flow && showQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedQueryFlow(message.query_flow),\n                                                        className: \"h-6 px-2 text-xs text-custom-blue-light hover:text-custom-blue hover:bg-custom-blue/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-3 h-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Details\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-black/40 rounded-full flex items-center justify-center border border-custom-blue/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-custom-blue-light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    placeholder: isSystemInitialized ? \"Ask me about your dormitory data...\" : \"Please initialize the system first\",\n                                    disabled: !isSystemInitialized,\n                                    className: \"flex-1 bg-black/40 border-custom-blue/30 text-white placeholder:text-gray-400 focus:border-custom-blue focus:ring-custom-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: !input.trim() || !isSystemInitialized,\n                                    className: \"bg-custom-blue hover:bg-custom-blue-dark px-6 border border-custom-blue/50 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bot_Database_Send_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        !isSystemInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-amber-400 mt-2\",\n                            children: \"Initialize the system from the control panel to start querying\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            selectedQueryFlow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-custom-blue/20 bg-black/30 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: \"Query Execution Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setSelectedQueryFlow(null),\n                                    className: \"text-gray-400 hover:text-white\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_query_flow_panel__WEBPACK_IMPORTED_MODULE_7__.QueryFlowPanel, {\n                            queryFlow: selectedQueryFlow,\n                            isVisible: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Samruddhi\\\\Final DormIQ\\\\Frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"OMVXCnMtObGtahIhyRKMJLAwMek=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});