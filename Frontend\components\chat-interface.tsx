"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, User, Bot, Database, Clock, Activity, Trash2 } from "lucide-react"
import { QueryFlowPanel } from "@/components/query-flow-panel"

interface QueryFlowStep {
  step: string
  description: string
  timestamp: string
}

interface QueryFlow {
  query: string
  databases_queried: string[]
  queries_executed: string[]
  data_sources_used: string[]
  processing_steps: QueryFlowStep[]
  execution_time: number
  success: boolean
  error?: string
}

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
  query_flow?: QueryFlow
}

interface ChatInterfaceProps {
  messages: Message[]
  onSendMessage: (content: string) => void
  onClearChat: () => void
  showQueryFlow: boolean
  isSystemInitialized: boolean
}

export function ChatInterface({ messages, onSendMessage, onClearChat, showQuery<PERSON>low, isSystemInitialized }: ChatInterfaceProps) {
  const [selectedQueryFlow, setSelectedQueryFlow] = useState<QueryFlow | null>(null)
  const [input, setInput] = useState("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && isSystemInitialized) {
      onSendMessage(input.trim())
      setInput("")
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="flex flex-col h-full bg-black/20">
      {/* Header */}
      <div className="border-b border-custom-blue/20 bg-black/50 backdrop-blur-sm p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-white">DormIQ</h1>
            <p className="text-sm text-custom-blue-light">Turning structure into story — through data..</p>
          </div>
          <div className="flex items-center gap-2">
            {/* <Badge variant="outline" className="gap-1 border-custom-blue/50 text-custom-blue-light">
              <Database className="w-3 h-3" />
              Graph DB
            </Badge>
            <Badge variant="outline" className="gap-1 border-custom-blue/50 text-custom-blue-light">
              <Clock className="w-3 h-3" />
              Time Series
            </Badge> */}
            {messages.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearChat}
                className="text-gray-400 hover:text-white hover:bg-red-500/20 ml-2"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4 max-w-4xl mx-auto">
          {messages.map((message) => (
            <div key={message.id} className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}>
              {message.role !== "user" && (
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    message.role === "system"
                      ? "bg-amber-500/20 text-amber-400 border border-amber-500/50"
                      : "bg-custom-blue/20 text-custom-blue-light border border-custom-blue/50"
                  }`}
                >
                  {message.role === "system" ? <Database className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                </div>
              )}

              <Card
                className={`max-w-[70%] p-4 ${
                  message.role === "user"
                    ? "bg-custom-blue text-white border-custom-blue/50 shadow-lg"
                    : message.role === "system"
                      ? "bg-amber-500/10 border-amber-500/30 text-white"
                      : "bg-black/40 border-custom-blue/20 text-white"
                }`}
              >
                <div className="space-y-3">
                  <p className="text-sm leading-relaxed">{message.content}</p>

                  {/* Query Flow Information - Inline with message */}
                  {message.query_flow && showQueryFlow && (
                    <div className="mt-3 p-3 bg-black/20 rounded-lg border border-custom-blue/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="w-4 h-4 text-custom-blue-light" />
                        <span className="text-xs font-medium text-custom-blue-light">Query Execution Flow</span>
                        <div className="flex items-center gap-1 ml-auto">
                          {message.query_flow.success ? (
                            <div className="flex items-center gap-1 text-xs text-green-400">
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                              Success
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-xs text-red-400">
                              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                              Failed
                            </div>
                          )}
                          <span className="text-xs text-gray-400 ml-2">
                            {message.query_flow.execution_time.toFixed(2)}s
                          </span>
                        </div>
                      </div>

                      {/* Databases Used */}
                      <div className="flex flex-wrap gap-1 mb-2">
                        {message.query_flow.databases_queried.length > 0 ? (
                          [...new Set(message.query_flow.databases_queried)].map((db, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs px-2 py-0 h-5 border-custom-blue/30 text-custom-blue-light"
                            >
                              <Database className="w-3 h-3 mr-1" />
                              {db}
                            </Badge>
                          ))
                        ) : (
                          <Badge
                            variant="outline"
                            className="text-xs px-2 py-0 h-5 border-purple-500/30 text-purple-400"
                          >
                            <Bot className="w-3 h-3 mr-1" />
                            LLM Knowledge
                          </Badge>
                        )}
                      </div>

                      {/* Processing Steps */}
                      <div className="text-xs text-gray-400">
                        {message.query_flow.processing_steps.length} steps: {
                          message.query_flow.processing_steps.map(step =>
                            step.step.replace('_', ' ')
                          ).join(' → ')
                        }
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className={`text-xs ${message.role === "user" ? "text-blue-200" : "text-gray-400"}`}>
                      {formatTime(message.timestamp)}
                    </div>
                    {message.query_flow && showQueryFlow && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedQueryFlow(message.query_flow!)}
                        className="h-6 px-2 text-xs text-custom-blue-light hover:text-custom-blue hover:bg-custom-blue/10"
                      >
                        <Activity className="w-3 h-3 mr-1" />
                        Details
                      </Button>
                    )}
                  </div>
                </div>
              </Card>

              {message.role === "user" && (
                <div className="w-8 h-8 bg-black/40 rounded-full flex items-center justify-center border border-custom-blue/30">
                  <User className="w-4 h-4 text-custom-blue-light" />
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t border-custom-blue/20 bg-black/50 p-4">
        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
          <div className="flex gap-3">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={
                isSystemInitialized ? "Ask me about your dormitory data..." : "Please initialize the system first"
              }
              disabled={!isSystemInitialized}
              className="flex-1 bg-black/40 border-custom-blue/30 text-white placeholder:text-gray-400 focus:border-custom-blue focus:ring-custom-blue"
            />
            <Button
              type="submit"
              disabled={!input.trim() || !isSystemInitialized}
              className="bg-custom-blue hover:bg-custom-blue-dark px-6 border border-custom-blue/50 shadow-lg"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          {!isSystemInitialized && (
            <p className="text-xs text-amber-400 mt-2">
              Initialize the system from the control panel to start querying
            </p>
          )}
        </form>
      </div>

      {/* Query Flow Panel */}
      {selectedQueryFlow && (
        <div className="border-t border-custom-blue/20 bg-black/30 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-white">Query Execution Analysis</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedQueryFlow(null)}
                className="text-gray-400 hover:text-white"
              >
                Close
              </Button>
            </div>
            <QueryFlowPanel queryFlow={selectedQueryFlow} isVisible={true} />
          </div>
        </div>
      )}
    </div>
  )
}
