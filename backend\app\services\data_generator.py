"""
Data Generator Service for DormIQ Analytics
Generates sample data for Neo4j and InfluxDB
"""

import logging
import asyncio
import random
import math
from datetime import datetime, timedelta
from typing import List, Dict, Any

from influxdb_client import Point, WritePrecision

from app.core.database import neo4j_manager, influxdb_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class DataGenerator:
    """Generates sample data for the DormIQ system"""

    def __init__(self):
        self.rooms = ["D001", "D002", "D003", "D004", "D005", "D006"]
        self.mechanical_rooms = ["M001", "M002"]
        self.ac_units = ["AC001", "AC002"]
        self.temperature_sensors = ["T001", "T002", "T003", "T004", "T005", "T006"]
        self.occupancy_sensors = ["O001", "O002", "O003", "O004", "O005", "O006"]

    async def generate_all_data(self):
        """Generate all sample data"""
        logger.info("Starting data generation...")

        try:
            # Generate Neo4j graph data
            await self.generate_neo4j_data()
            logger.info("Neo4j data generated successfully")

            # Generate InfluxDB time series data
            await self.generate_influxdb_data()
            logger.info("InfluxDB data generated successfully")

            logger.info("All data generation completed")

        except Exception as e:
            logger.error(f"Data generation failed: {e}")
            raise

    async def clear_all_data(self):
        """Clear all data from databases"""
        logger.info("Clearing all data...")

        try:
            # Clear Neo4j data
            await self.clear_neo4j_data()

            # Clear InfluxDB data (optional - usually not needed for time series)
            # await self.clear_influxdb_data()

            logger.info("Data cleared successfully")

        except Exception as e:
            logger.error(f"Data clearing failed: {e}")
            raise

    async def generate_neo4j_data(self):
        """Generate Neo4j graph data"""
        try:
            # Clear existing data first
            await self.clear_neo4j_data()

            # Create nodes and relationships
            queries = [
                # Create Room nodes
                *[f"CREATE (r:Room {{id: '{room}', type: 'dormitory', side: '{self._get_room_side(room)}'}})"
                  for room in self.rooms],

                # Create Mechanical Room nodes
                *[f"CREATE (m:MechanicalRoom {{id: '{room}', type: 'mechanical'}})"
                  for room in self.mechanical_rooms],

                # Create AC Unit nodes
                "CREATE (ac1:ACUnit {id: 'AC001', model: 'CoolMax-3000', capacity: '3.5kW', location: 'M001'})",
                "CREATE (ac2:ACUnit {id: 'AC002', model: 'CoolMax-3000', capacity: '3.5kW', location: 'M002'})",

                # Create Temperature Sensor nodes
                *[f"CREATE (t:TemperatureSensor {{id: '{sensor}', type: 'temperature', unit: 'celsius', accuracy: '±0.5°C'}})"
                  for sensor in self.temperature_sensors],

                # Create Occupancy Sensor nodes
                *[f"CREATE (o:OccupancySensor {{id: '{sensor}', type: 'occupancy', technology: 'PIR', range: '5m'}})"
                  for sensor in self.occupancy_sensors],

                # Create relationships: AC units service rooms
                "MATCH (ac:ACUnit {id: 'AC001'}), (r1:Room {id: 'D001'}), (r2:Room {id: 'D002'}), (r3:Room {id: 'D003'}) CREATE (ac)-[:SERVICES]->(r1), (ac)-[:SERVICES]->(r2), (ac)-[:SERVICES]->(r3)",
                "MATCH (ac:ACUnit {id: 'AC002'}), (r4:Room {id: 'D004'}), (r5:Room {id: 'D005'}), (r6:Room {id: 'D006'}) CREATE (ac)-[:SERVICES]->(r4), (ac)-[:SERVICES]->(r5), (ac)-[:SERVICES]->(r6)",

                # Create relationships: Rooms have sensors
                *[f"MATCH (r:Room {{id: '{room}'}}), (t:TemperatureSensor {{id: 'T{room[1:]}'}}), (o:OccupancySensor {{id: 'O{room[1:]}'}}) CREATE (r)-[:HAS_SENSOR]->(t), (r)-[:HAS_SENSOR]->(o)"
                  for room in self.rooms],

                # Create relationships: Sensors monitor rooms
                *[f"MATCH (t:TemperatureSensor {{id: 'T{room[1:]}'}}), (r:Room {{id: '{room}'}}) CREATE (t)-[:MONITORS]->(r)"
                  for room in self.rooms],
                *[f"MATCH (o:OccupancySensor {{id: 'O{room[1:]}'}}), (r:Room {{id: '{room}'}}) CREATE (o)-[:MONITORS]->(r)"
                  for room in self.rooms],

                # Create relationships: AC units located in mechanical rooms
                "MATCH (ac1:ACUnit {id: 'AC001'}), (m1:MechanicalRoom {id: 'M001'}) CREATE (ac1)-[:LOCATED_IN]->(m1)",
                "MATCH (ac2:ACUnit {id: 'AC002'}), (m2:MechanicalRoom {id: 'M002'}) CREATE (ac2)-[:LOCATED_IN]->(m2)",

                # Create direct relationships: Sensors to AC units (NEW REQUIREMENT)
                # AC001 monitors sensors in rooms D001-D003
                "MATCH (ac:ACUnit {id: 'AC001'}), (t1:TemperatureSensor {id: 'T001'}), (t2:TemperatureSensor {id: 'T002'}), (t3:TemperatureSensor {id: 'T003'}) CREATE (ac)-[:MONITORS_SENSOR]->(t1), (ac)-[:MONITORS_SENSOR]->(t2), (ac)-[:MONITORS_SENSOR]->(t3)",
                "MATCH (ac:ACUnit {id: 'AC001'}), (o1:OccupancySensor {id: 'O001'}), (o2:OccupancySensor {id: 'O002'}), (o3:OccupancySensor {id: 'O003'}) CREATE (ac)-[:MONITORS_SENSOR]->(o1), (ac)-[:MONITORS_SENSOR]->(o2), (ac)-[:MONITORS_SENSOR]->(o3)",

                # AC002 monitors sensors in rooms D004-D006
                "MATCH (ac:ACUnit {id: 'AC002'}), (t4:TemperatureSensor {id: 'T004'}), (t5:TemperatureSensor {id: 'T005'}), (t6:TemperatureSensor {id: 'T006'}) CREATE (ac)-[:MONITORS_SENSOR]->(t4), (ac)-[:MONITORS_SENSOR]->(t5), (ac)-[:MONITORS_SENSOR]->(t6)",
                "MATCH (ac:ACUnit {id: 'AC002'}), (o4:OccupancySensor {id: 'O004'}), (o5:OccupancySensor {id: 'O005'}), (o6:OccupancySensor {id: 'O006'}) CREATE (ac)-[:MONITORS_SENSOR]->(o4), (ac)-[:MONITORS_SENSOR]->(o5), (ac)-[:MONITORS_SENSOR]->(o6)"
            ]

            # Execute all queries
            for query in queries:
                await neo4j_manager.execute_query(query)

            logger.info("Neo4j graph structure created successfully")

        except Exception as e:
            logger.error(f"Neo4j data generation failed: {e}")
            raise

    async def generate_influxdb_data(self):
        """Generate InfluxDB time series data"""
        try:
            # Generate data for the last 7 days at 5-minute intervals
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)

            points = []
            current_time = start_time

            while current_time <= end_time:
                # Generate temperature data
                for i, room in enumerate(self.rooms):
                    temp_value = self._generate_temperature_value(current_time, room)

                    point = Point("temperature") \
                        .tag("sensor_id", f"T{room[1:]}") \
                        .tag("room", room) \
                        .tag("side", self._get_room_side(room)) \
                        .field("value", temp_value) \
                        .field("unit", "celsius") \
                        .time(current_time, WritePrecision.S)

                    points.append(point)

                # Generate occupancy data
                for i, room in enumerate(self.rooms):
                    occupancy_value = self._generate_occupancy_value(current_time, room)

                    point = Point("occupancy") \
                        .tag("sensor_id", f"O{room[1:]}") \
                        .tag("room", room) \
                        .tag("side", self._get_room_side(room)) \
                        .field("value", occupancy_value) \
                        .field("occupied", occupancy_value == 1) \
                        .time(current_time, WritePrecision.S)

                    points.append(point)

                current_time += timedelta(minutes=5)

            # Write points to InfluxDB in batches
            batch_size = 1000
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                await influxdb_manager.write_api.write(
                    bucket=settings.INFLUXDB_BUCKET,
                    org=settings.INFLUXDB_ORG,
                    record=batch
                )

                # Small delay to avoid overwhelming the database
                await asyncio.sleep(0.1)

            logger.info(f"Generated {len(points)} time series data points")

        except Exception as e:
            logger.error(f"InfluxDB data generation failed: {e}")
            raise

    async def clear_neo4j_data(self):
        """Clear all Neo4j data"""
        try:
            query = "MATCH (n) DETACH DELETE n"
            await neo4j_manager.execute_query(query)
            logger.info("Neo4j data cleared")
        except Exception as e:
            logger.error(f"Failed to clear Neo4j data: {e}")
            raise

    def _get_room_side(self, room: str) -> str:
        """Get the side of the building for a room"""
        room_num = int(room[1:])
        return "sunny" if room_num <= 3 else "shaded"

    def _generate_temperature_value(self, timestamp: datetime, room: str) -> float:
        """Generate realistic temperature value"""
        # Base temperature with daily cycle
        hour = timestamp.hour
        minute = timestamp.minute

        # Daily temperature cycle (sine wave)
        time_of_day = hour + minute / 60.0
        daily_cycle = 3 * math.sin((time_of_day - 6) * math.pi / 12)

        # Base temperature
        base_temp = 20.0

        # Room-specific adjustments
        if self._get_room_side(room) == "sunny":
            # Sunny side rooms are warmer, especially in afternoon
            if 12 <= hour <= 18:
                side_adjustment = 2.5 + random.uniform(0, 1.5)
            else:
                side_adjustment = 1.0 + random.uniform(0, 1.0)
        else:
            # Shaded side rooms
            side_adjustment = random.uniform(-0.5, 0.5)

        # Random noise
        noise = random.uniform(-0.8, 0.8)

        # Seasonal adjustment (assuming it's summer)
        seasonal_adjustment = 2.0

        final_temp = base_temp + daily_cycle + side_adjustment + noise + seasonal_adjustment

        # Ensure reasonable bounds
        return max(18.0, min(32.0, round(final_temp, 1)))

    def _generate_occupancy_value(self, timestamp: datetime, room: str) -> int:
        """Generate realistic occupancy value (0 or 1)"""
        hour = timestamp.hour
        day_of_week = timestamp.weekday()  # 0 = Monday, 6 = Sunday

        # Different patterns for different rooms
        room_num = int(room[1:])

        # Full-time student pattern (rooms D001, D003, D005)
        if room_num in [1, 3, 5]:
            if day_of_week < 5:  # Weekdays
                if 7 <= hour <= 9 or 13 <= hour <= 15 or 20 <= hour <= 22:
                    probability = 0.8
                elif 23 <= hour or hour <= 6:
                    probability = 0.9
                else:
                    probability = 0.2
            else:  # Weekends
                if 9 <= hour <= 11 or 14 <= hour <= 16 or 21 <= hour <= 23:
                    probability = 0.7
                elif 0 <= hour <= 8:
                    probability = 0.9
                else:
                    probability = 0.4

        # Night student pattern (rooms D002, D004, D006)
        else:
            if day_of_week < 5:  # Weekdays
                if 6 <= hour <= 8 or 16 <= hour <= 18 or 21 <= hour <= 23:
                    probability = 0.8
                elif 0 <= hour <= 5:
                    probability = 0.9
                else:
                    probability = 0.1
            else:  # Weekends
                if 10 <= hour <= 12 or 18 <= hour <= 20:
                    probability = 0.6
                elif 1 <= hour <= 7:
                    probability = 0.9
                else:
                    probability = 0.3

        # Add some randomness
        return 1 if random.random() < probability else 0
