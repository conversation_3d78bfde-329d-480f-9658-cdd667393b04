#!/usr/bin/env python3
"""
Direct test of prediction and student profile queries
Tests with hardcoded working queries to verify functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api"

def test_working_flux_query():
    """Test a known working Flux query directly"""
    print("🧪 Testing working Flux query...")
    
    # Test with a simple working query
    payload = {
        "message": "show me temperature data",
        "session_id": "direct_test"
    }
    
    response = requests.post(f"{BASE_URL}/chat", json=payload)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Basic query works: {result.get('content', '')[:100]}...")
        return True
    else:
        print(f"❌ Basic query failed: {response.status_code}")
        return False

def test_prediction_detection():
    """Test if prediction queries are correctly detected"""
    print("\n🔮 Testing prediction query detection...")
    
    prediction_queries = [
        "predict temperature",
        "forecast tomorrow",
        "what will happen next",
        "trend analysis",
        "expect temperature"
    ]
    
    for query in prediction_queries:
        payload = {
            "message": query,
            "session_id": "prediction_test"
        }
        
        response = requests.post(f"{BASE_URL}/chat", json=payload)
        if response.status_code == 200:
            result = response.json()
            query_type = result.get('query_type')
            print(f"Query: '{query}' -> Type: {query_type}")
            
            if query_type == 'predictive':
                print(f"✅ Correctly identified as predictive")
            else:
                print(f"⚠️  Not identified as predictive (got: {query_type})")
        else:
            print(f"❌ Query failed: {query}")

def test_student_profile_detection():
    """Test if student profile queries work"""
    print("\n👥 Testing student profile queries...")
    
    student_queries = [
        "show full-time students",
        "working-night student patterns",
        "occupancy by student type",
        "student profile analysis"
    ]
    
    for query in student_queries:
        payload = {
            "message": query,
            "session_id": "student_test"
        }
        
        response = requests.post(f"{BASE_URL}/chat", json=payload)
        if response.status_code == 200:
            result = response.json()
            content = result.get('content', '').lower()
            
            has_student_content = any(word in content for word in ['full-time', 'working-night', 'student'])
            
            print(f"Query: '{query}'")
            if has_student_content:
                print(f"✅ Contains student profile content")
            else:
                print(f"⚠️  May lack student profile content")
            
            print(f"Response: {result.get('content', '')[:150]}...")
        else:
            print(f"❌ Query failed: {query}")

def test_system_initialization():
    """Test system initialization"""
    print("\n🔄 Testing system initialization...")
    
    response = requests.post(f"{BASE_URL}/initialize")
    if response.status_code == 200:
        print("✅ System initialization successful")
        time.sleep(3)  # Wait for data generation
        return True
    else:
        print(f"❌ System initialization failed: {response.status_code}")
        return False

def main():
    print("🚀 Direct Query Testing for Enhanced Features")
    print("=" * 60)
    
    # Initialize system first
    if not test_system_initialization():
        print("❌ Cannot proceed without system initialization")
        return
    
    # Test basic functionality
    if not test_working_flux_query():
        print("❌ Basic queries not working")
        return
    
    # Test prediction detection
    test_prediction_detection()
    
    # Test student profile queries
    test_student_profile_detection()
    
    print("\n✅ Direct testing completed")

if __name__ == "__main__":
    main()
