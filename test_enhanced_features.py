#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced DormIQ Features
Tests prediction queries and student profile queries
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000/api"
HEADERS = {"Content-Type": "application/json"}

def test_api_connection():
    """Test if the API is accessible"""
    try:
        response = requests.get(f"{BASE_URL.replace('/api', '')}/health")
        if response.status_code == 200:
            print("✅ API Connection: SUCCESS")
            return True
        else:
            print(f"❌ API Connection: FAILED (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ API Connection: FAILED (Error: {e})")
        return False

def initialize_system():
    """Initialize the system with fresh data"""
    try:
        print("\n🔄 Initializing system with fresh data...")
        response = requests.post(f"{BASE_URL}/initialize")
        if response.status_code == 200:
            print("✅ System Initialization: SUCCESS")
            time.sleep(2)  # Wait for data generation
            return True
        else:
            print(f"❌ System Initialization: FAILED (Status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ System Initialization: FAILED (Error: {e})")
        return False

def send_chat_query(query, session_id="test_session"):
    """Send a chat query and return the response"""
    try:
        payload = {
            "message": query,
            "session_id": session_id
        }
        response = requests.post(f"{BASE_URL}/chat", json=payload, headers=HEADERS)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Query failed: {query} (Status: {response.status_code})")
            return None
    except Exception as e:
        print(f"❌ Query error: {query} (Error: {e})")
        return None

def test_prediction_queries():
    """Test prediction and forecasting capabilities"""
    print("\n" + "="*60)
    print("🔮 TESTING PREDICTION QUERIES")
    print("="*60)
    
    prediction_queries = [
        "predict tomorrow's temperature in room D001",
        "forecast the occupancy for next week in room D003",
        "what will be the temperature trend for sunny rooms?",
        "predict when room D002 will be most occupied tomorrow",
        "forecast the average temperature for the next 3 days",
        "will room D005 be warmer tomorrow than today?",
        "predict the peak occupancy hours for working-night students",
        "what temperature should we expect in mechanical rooms tomorrow?"
    ]
    
    results = []
    for i, query in enumerate(prediction_queries, 1):
        print(f"\n📊 Test {i}/8: {query}")
        response = send_chat_query(query)
        
        if response:
            query_type = response.get('query_type', 'unknown')
            content = response.get('content', '')
            
            # Check if it's correctly identified as predictive
            is_predictive = query_type == 'predictive'
            has_prediction_content = any(word in content.lower() for word in 
                                       ['predict', 'forecast', 'tomorrow', 'future', 'trend', 'expect'])
            
            if is_predictive:
                print(f"✅ Query Type: {query_type} (CORRECT)")
            else:
                print(f"⚠️  Query Type: {query_type} (Expected: predictive)")
            
            if has_prediction_content:
                print(f"✅ Response contains prediction content")
            else:
                print(f"⚠️  Response may lack prediction content")
            
            print(f"📝 Response: {content[:200]}...")
            
            results.append({
                'query': query,
                'query_type': query_type,
                'is_predictive': is_predictive,
                'has_prediction_content': has_prediction_content,
                'response_length': len(content)
            })
        else:
            print(f"❌ Failed to get response")
            results.append({
                'query': query,
                'query_type': 'failed',
                'is_predictive': False,
                'has_prediction_content': False,
                'response_length': 0
            })
    
    return results

def test_student_profile_queries():
    """Test student profile integration in occupancy analysis"""
    print("\n" + "="*60)
    print("👥 TESTING STUDENT PROFILE QUERIES")
    print("="*60)
    
    student_profile_queries = [
        "show occupancy patterns for full-time students",
        "what are the occupancy rates for working-night students?",
        "compare occupancy between full-time and working-night students",
        "which rooms have full-time students?",
        "when are working-night students most active?",
        "show me the occupancy schedule for full-time students",
        "what's the difference in occupancy patterns between student types?",
        "which student profile has higher occupancy during afternoon?"
    ]
    
    results = []
    for i, query in enumerate(student_profile_queries, 1):
        print(f"\n👤 Test {i}/8: {query}")
        response = send_chat_query(query)
        
        if response:
            query_type = response.get('query_type', 'unknown')
            content = response.get('content', '')
            
            # Check if response mentions student profiles
            has_student_profile_content = any(word in content.lower() for word in 
                                            ['full-time', 'working-night', 'student profile', 'student type'])
            
            # Check if it uses time series data (occupancy patterns)
            uses_time_series = query_type in ['time_series', 'multi_database']
            
            print(f"📊 Query Type: {query_type}")
            
            if has_student_profile_content:
                print(f"✅ Response mentions student profiles")
            else:
                print(f"⚠️  Response may lack student profile differentiation")
            
            if uses_time_series:
                print(f"✅ Uses appropriate data source (time series)")
            else:
                print(f"⚠️  May not be using time series data")
            
            print(f"📝 Response: {content[:200]}...")
            
            results.append({
                'query': query,
                'query_type': query_type,
                'has_student_profile_content': has_student_profile_content,
                'uses_time_series': uses_time_series,
                'response_length': len(content)
            })
        else:
            print(f"❌ Failed to get response")
            results.append({
                'query': query,
                'query_type': 'failed',
                'has_student_profile_content': False,
                'uses_time_series': False,
                'response_length': 0
            })
    
    return results

def generate_test_report(prediction_results, student_profile_results):
    """Generate a comprehensive test report"""
    print("\n" + "="*80)
    print("📋 COMPREHENSIVE TEST REPORT")
    print("="*80)
    
    # Prediction Query Analysis
    print("\n🔮 PREDICTION QUERIES ANALYSIS:")
    prediction_success = sum(1 for r in prediction_results if r['is_predictive'])
    prediction_content_success = sum(1 for r in prediction_results if r['has_prediction_content'])
    
    print(f"✅ Correctly identified as predictive: {prediction_success}/{len(prediction_results)}")
    print(f"✅ Contains prediction content: {prediction_content_success}/{len(prediction_results)}")
    print(f"📊 Average response length: {sum(r['response_length'] for r in prediction_results) / len(prediction_results):.0f} chars")
    
    # Student Profile Query Analysis
    print("\n👥 STUDENT PROFILE QUERIES ANALYSIS:")
    profile_content_success = sum(1 for r in student_profile_results if r['has_student_profile_content'])
    time_series_usage = sum(1 for r in student_profile_results if r['uses_time_series'])
    
    print(f"✅ Contains student profile content: {profile_content_success}/{len(student_profile_results)}")
    print(f"✅ Uses time series data: {time_series_usage}/{len(student_profile_results)}")
    print(f"📊 Average response length: {sum(r['response_length'] for r in student_profile_results) / len(student_profile_results):.0f} chars")
    
    # Overall Assessment
    total_tests = len(prediction_results) + len(student_profile_results)
    successful_tests = prediction_success + profile_content_success
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"📈 Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    
    if successful_tests/total_tests >= 0.8:
        print("🎉 EXCELLENT: Enhanced features are working well!")
    elif successful_tests/total_tests >= 0.6:
        print("👍 GOOD: Enhanced features are mostly functional")
    else:
        print("⚠️  NEEDS IMPROVEMENT: Enhanced features need refinement")

def main():
    """Main test execution"""
    print("🚀 DormIQ Enhanced Features Test Suite")
    print("Testing Prediction Queries and Student Profile Integration")
    print("=" * 80)
    
    # Test API connection
    if not test_api_connection():
        print("❌ Cannot proceed without API connection")
        return
    
    # Initialize system
    if not initialize_system():
        print("❌ Cannot proceed without system initialization")
        return
    
    # Run tests
    prediction_results = test_prediction_queries()
    student_profile_results = test_student_profile_queries()
    
    # Generate report
    generate_test_report(prediction_results, student_profile_results)
    
    print(f"\n✅ Test completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
