"""
LLM Manager with <PERSON><PERSON>hain integration for Google Gemini
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain.prompts import PromptTemplate
from langchain.schema import BaseMessage, HumanMessage, AIMessage

from .config import settings
from .database import neo4j_manager, influxdb_manager

logger = logging.getLogger(__name__)


class LLMManager:
    """LLM Manager with LangChain integration"""

    def __init__(self):
        self.llm = None
        self.agent_executor = None
        self.tools = []
        self.initialized = False
        self.current_query_flow = None  # Track current query flow

    async def initialize(self):
        """Initialize LLM and LangChain components"""
        try:
            # Initialize Google Gemini LLM
            self.llm = ChatGoogleGenerativeAI(
                model=settings.LLM_MODEL,
                google_api_key=settings.GOOGLE_API_KEY,
                temperature=settings.LLM_TEMPERATURE,
                max_tokens=settings.LLM_MAX_TOKENS
            )

            # Create custom tools
            self.tools = [
                self._create_neo4j_tool(),
                self._create_influxdb_tool(),
                self._create_analysis_tool()
            ]

            # Create agent
            self.agent_executor = self._create_agent()

            self.initialized = True
            logger.info("LLM Manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize LLM Manager: {e}")
            raise

    async def health_check(self) -> Dict[str, Any]:
        """Check LLM health"""
        if not self.initialized:
            return {"status": "not_initialized", "error": "LLM not initialized"}



        try:
            # Test with a simple query
            response = await self.llm.ainvoke([HumanMessage(content="Hello")])
            return {"status": "connected", "model": settings.LLM_MODEL}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def process_query(self, user_query: str) -> Dict[str, Any]:
        """Process user query using LangChain agent"""
        if not self.initialized:
            raise Exception("LLM Manager not initialized")



        # Initialize query flow tracking
        query_flow = {
            "query": user_query,
            "start_time": datetime.now(),
            "databases_queried": [],
            "queries_executed": [],
            "data_sources_used": [],
            "processing_steps": [],
            "execution_time": 0,
            "success": False
        }

        try:
            # Set current query flow for tools to access
            self.current_query_flow = query_flow

            # Add initial step
            query_flow["processing_steps"].append({
                "step": "query_analysis",
                "description": "Analyzing user query and determining data sources",
                "timestamp": datetime.now().isoformat()
            })

            # Use the agent to process the query
            result = await self.agent_executor.ainvoke({
                "input": user_query,
                "chat_history": []
            })

            # Calculate execution time
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = True

            # Add completion step
            query_flow["processing_steps"].append({
                "step": "response_generation",
                "description": "Generated final response from collected data",
                "timestamp": end_time.isoformat()
            })

            # Clear current query flow
            self.current_query_flow = None

            return {
                "response": result["output"],
                "query_type": self._determine_query_type(user_query),
                "query_flow": query_flow,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            # Calculate execution time even on error
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = False
            query_flow["error"] = str(e)

            logger.error(f"Query processing failed: {e}")

            # Clear current query flow
            self.current_query_flow = None

            # No fallback - raise the actual error for production debugging

            return {
                "response": "I apologize, but I encountered an error while processing your query. Please try rephrasing your question or check if the system is properly initialized.",
                "error": str(e),
                "query_flow": query_flow,
                "timestamp": datetime.now().isoformat()
            }

    def _create_neo4j_tool(self) -> Tool:
        """Create Neo4j query tool"""
        async def neo4j_query(query_description: str) -> str:
            """Query the Neo4j graph database for dormitory structure and relationships"""
            try:
                # Generate actual Cypher query based on user description
                cypher_query = self._generate_cypher_query(query_description)
                logger.info(f"Executing Neo4j query: {cypher_query}")

                # Execute the query against Neo4j database
                results = await neo4j_manager.execute_query(cypher_query)

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("Neo4j")
                    self.current_query_flow["queries_executed"].append(cypher_query)
                    self.current_query_flow["data_sources_used"].append("Graph Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried Neo4j graph database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_neo4j_results(results)
                logger.info(f"Neo4j query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying Neo4j: {str(e)}"
                logger.error(error_msg)
                return error_msg

        # Create a sync wrapper for the async function
        def sync_neo4j_query(query_description: str) -> str:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(neo4j_query(query_description))
            except RuntimeError:
                # If no event loop is running, create a new one
                return asyncio.run(neo4j_query(query_description))

        return Tool(
            name="neo4j_query",
            description="Query the Neo4j graph database for information about dormitory rooms, AC units, sensors, and their relationships. Use this for structural queries about: room layouts, building structure, which rooms face the sun, room orientation (sunny/shaded sides), AC unit connections, sensor assignments, and any questions about what is connected to what or room properties. IMPORTANT: Pass the user's original question as the query_description, not a Cypher query.",
            func=sync_neo4j_query
        )

    def _create_influxdb_tool(self) -> Tool:
        """Create InfluxDB query tool"""
        async def influxdb_query(query_description: str) -> str:
            """Query the InfluxDB time series database for sensor data and trends"""
            try:
                # Generate actual Flux query based on user description
                flux_query = self._generate_flux_query(query_description)
                logger.info(f"Executing InfluxDB query: {flux_query}")

                # Execute the query against InfluxDB database
                results = await influxdb_manager.execute_query(flux_query)

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("InfluxDB")
                    self.current_query_flow["queries_executed"].append(flux_query)
                    self.current_query_flow["data_sources_used"].append("Time Series Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried InfluxDB time series database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_influxdb_results(results)
                logger.info(f"InfluxDB query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying InfluxDB: {str(e)}"
                logger.error(error_msg)
                return error_msg

        # Create a sync wrapper for the async function
        def sync_influxdb_query(query_description: str) -> str:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(influxdb_query(query_description))
            except RuntimeError:
                # If no event loop is running, create a new one
                return asyncio.run(influxdb_query(query_description))

        return Tool(
            name="influxdb_query",
            description="Query the InfluxDB time series database for temperature readings, occupancy data, temporal patterns, and predictive analysis. Use this for: hot temperature analysis, peak time identification, daily patterns, room temperature comparisons, occupancy schedules, time-based trends, forecasting, and predictive modeling. Perfect for queries about 'when', 'times of day', 'hot temperatures', 'peak hours', 'predict', 'forecast', 'tomorrow', 'next week', 'future trends', and temporal analysis.",
            func=sync_influxdb_query
        )



    def _create_analysis_tool(self) -> Tool:
        """Create data analysis tool"""
        def analyze_data(data_description: str) -> str:
            """Analyze and interpret data patterns for insights"""
            # This tool helps with data interpretation and insights
            return f"Analysis of {data_description}: Based on the data patterns, I can provide insights about dormitory operations, energy efficiency, and occupancy trends."

        return Tool(
            name="analyze_data",
            description="Analyze and interpret data to provide insights about dormitory operations, efficiency, and patterns.",
            func=analyze_data
        )

    def _create_agent(self) -> AgentExecutor:
        """Create LangChain agent"""
        prompt = PromptTemplate.from_template("""
You are DormIQ Analytics Assistant, a friendly and conversational AI that helps analyze dormitory data.
You have access to both graph database (Neo4j) and time series database (InfluxDB) containing information about:

- 8 rooms total: 6 dorm rooms (D001-D006), 2 mechanical rooms (M001-M002)
- 2 Air Conditioning Units (AC001 in M001, AC002 in M002)
- AC001 services rooms D001, D002, D003
- AC002 services rooms D004, D005, D006
- Temperature sensors T001-T006 (one per dorm room)
- Occupancy sensors O001-O006 (one per dorm room)

You are a friendly dormitory infrastructure analyst with access to Neo4j graph database and InfluxDB time series database.

IMPORTANT: Only use database tools when the user asks specific questions about dormitory data. For greetings, general conversation, or non-data questions, respond directly without using any tools.

CRITICAL RULES:
1. For greetings/general chat: respond directly WITHOUT using any tools
2. For data questions: use ONLY ONE tool call to get the information needed
3. Never make multiple tool calls - be decisive with your first query
4. If you need both graph and time series data, prioritize the most relevant one

WHEN TO USE TOOLS:
- Structural questions (rooms, AC units, sensors) → Use neo4j_query with Cypher
- Data questions (temperature, occupancy, trends) → Use influxdb_query with Flux
- For correlations: choose the most relevant database (usually InfluxDB for temperature correlations)

WHEN NOT TO USE TOOLS:
- Greetings: "hi", "hello", "hey"
- General questions: "what can you do", "help me"
- Explanations that don't need live data

EFFICIENCY RULES:
- Make ONE targeted query that gets exactly what you need
- Don't retry queries or make multiple calls
- Work with whatever data you get from the first query
- Be confident in your analysis from the available data

Available tools:
{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
{agent_scratchpad}
        """)

        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            max_iterations=4,  # Allow for multi-database queries: Neo4j + InfluxDB + response generation
            max_execution_time=20,  # 20 second timeout for complex queries
            handle_parsing_errors=True  # Handle parsing errors gracefully
        )

    def _determine_query_type(self, query: str) -> str:
        """Determine the type of query"""
        query_lower = query.lower()
        if any(word in query_lower for word in ["temperature", "hot", "cold", "trend", "time", "when", "occupancy", "occupied"]):
            return "time_series"
        elif any(word in query_lower for word in ["room", "ac", "unit", "connected", "services", "relationship"]):
            return "graph"
        else:
            return "general"

    def _generate_cypher_query(self, description: str) -> str:
        """Generate actual Cypher queries based on user description"""
        description_lower = description.lower()

        # Room count queries - Count ALL rooms (dormitory + mechanical)
        if "how many" in description_lower and "room" in description_lower:
            return """
            MATCH (r:Room)
            OPTIONAL MATCH (m:MechanicalRoom)
            RETURN count(r) + count(m) as total_rooms,
                   count(r) as dormitory_rooms,
                   count(m) as mechanical_rooms
            """

        # AC unit and room relationships
        elif "ac001" in description_lower and ("room" in description_lower or "service" in description_lower):
            return """
            MATCH (ac:ACUnit {id: 'AC001'})-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, collect(room.id) as rooms_serviced
            """

        elif "ac002" in description_lower and ("room" in description_lower or "service" in description_lower):
            return """
            MATCH (ac:ACUnit {id: 'AC002'})-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, collect(room.id) as rooms_serviced
            """

        # Which AC services which room
        elif any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]) and "ac" in description_lower:
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f"""
            MATCH (ac:ACUnit)-[:SERVICES]->(room:Room {{id: '{room_id}'}})
            RETURN ac.id as ac_unit, room.id as room_id
            """

        # Sensor count queries
        elif "sensor" in description_lower and "count" in description_lower:
            if "ac" in description_lower:
                # Use direct AC-sensor relationship
                return """
                MATCH (ac:ACUnit)-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, count(sensor) as sensor_count
                """
            else:
                return """
                MATCH (room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN room.id as room_id, count(sensor) as sensor_count
                ORDER BY room.id
                """

        # Direct sensor-AC relationship queries (NEW)
        elif ("sensor" in description_lower and "ac" in description_lower and ("monitor" in description_lower or "which" in description_lower)) or ("which" in description_lower and "sensor" in description_lower and "ac001" in description_lower):
            if "ac001" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC001'})-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                """
            elif "ac002" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC002'})-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                """
            else:
                return """
                MATCH (ac:ACUnit)-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                ORDER BY ac.id
                """

        # Sensor information queries
        elif "sensor" in description_lower and "report" in description_lower:
            if "ac001" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC001'})-[:SERVICES]->(room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN sensor.id as sensor_id, sensor.type as sensor_type, room.id as room_id
                ORDER BY sensor.id
                """
            elif "ac002" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC002'})-[:SERVICES]->(room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN sensor.id as sensor_id, sensor.type as sensor_type, room.id as room_id
                ORDER BY sensor.id
                """
            else:
                return """
                MATCH (sensor)-[:REPORTS_TO]->(ac:ACUnit)
                RETURN sensor.id as sensor_id, ac.id as ac_unit
                ORDER BY sensor.id
                """

        # Room side information (sunny vs shaded) and sun-facing queries
        elif any(word in description_lower for word in ["side", "sunny", "shaded", "sun", "facing", "east", "west"]):
            return """
            MATCH (room:Room)
            RETURN room.id as room_id, room.side as side, room.type as room_type,
                   room.faces_sun as faces_sun, room.building_side as building_side
            ORDER BY room.side, room.id
            """

        # Building layout and structure
        elif "layout" in description_lower or "structure" in description_lower or "building" in description_lower:
            return """
            MATCH (n)
            OPTIONAL MATCH (n)-[r]->(m)
            RETURN labels(n) as node_type, n.id as id, properties(n) as properties,
                   type(r) as relationship, labels(m) as target_type, m.id as target_id
            ORDER BY n.id
            """

        # AC unit information
        elif "ac unit" in description_lower or "air conditioning" in description_lower:
            return """
            MATCH (ac:ACUnit)
            OPTIONAL MATCH (ac)-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, properties(ac) as ac_properties, collect(room.id) as rooms_serviced
            ORDER BY ac.id
            """

        # Room information
        elif "room" in description_lower and not any(specific in description_lower for specific in ["temperature", "occupancy", "hot", "cold"]):
            return """
            MATCH (room:Room)
            OPTIONAL MATCH (room)-[:HAS_SENSOR]->(sensor)
            RETURN room.id as room_id, room.side as side, room.type as room_type,
                   collect(sensor.id) as sensors
            ORDER BY room.id
            """

        # Connection and relationship queries
        elif "connection" in description_lower or "relationship" in description_lower or "connect" in description_lower:
            return """
            MATCH (a)-[r]->(b)
            RETURN a.id as source, type(r) as relationship, b.id as target,
                   labels(a) as source_type, labels(b) as target_type
            ORDER BY a.id, type(r)
            """

        # Default comprehensive query for complex questions
        else:
            return """
            MATCH (room:Room)
            OPTIONAL MATCH (ac:ACUnit)-[:SERVICES]->(room)
            OPTIONAL MATCH (room)-[:HAS_SENSOR]->(sensor)
            RETURN room.id as room_id, room.side as side, ac.id as ac_unit,
                   collect(sensor.id) as sensors
            ORDER BY room.id
            """



    def _generate_flux_query(self, description: str) -> str:
        """Generate actual Flux queries based on user description"""
        description_lower = description.lower()
        bucket = settings.INFLUXDB_BUCKET

        # Specific room temperature queries
        if "temperature" in description_lower and any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]):
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> filter(fn: (r) => r["room"] == "{room_id}")
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Correlation between location and temperature
        elif "correlation" in description_lower and "location" in description_lower and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room", "side"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "mean")
            '''

        # Sunny side vs shaded side temperature comparison
        elif ("sunny" in description_lower or "shaded" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["side"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Hot temperature analysis
        elif ("hot" in description_lower or "peak" in description_lower or "highest" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> filter(fn: (r) => r["_value"] > 25.0)
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: max)
              |> yield(name: "max")
            '''

        # Time-based temperature patterns
        elif ("time" in description_lower or "when" in description_lower or "times of day" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> map(fn: (r) => ({{ r with hour_of_day: uint(v: time.hour(t: r._time)) }}))
              |> group(columns: ["hour_of_day"])
              |> mean()
              |> yield(name: "hourly_average")
            '''

        # Predictive temperature analysis
        elif ("predict" in description_lower or "forecast" in description_lower or "tomorrow" in description_lower or "future" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -14d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> sort(columns: ["_time"])
              |> yield(name: "historical_for_prediction")
            '''

        # General temperature queries
        elif "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Specific room occupancy queries
        elif "occupancy" in description_lower and any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]):
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -48h)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> filter(fn: (r) => r["room"] == "{room_id}")
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "occupancy_rate")
            '''

        # Student profile-based occupancy
        elif "occupancy" in description_lower and ("full-time" in description_lower or "working night" in description_lower):
            profile_type = "full-time" if "full-time" in description_lower else "working-night"
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> filter(fn: (r) => r["profile_type"] == "{profile_type}")
              |> group(columns: ["room", "profile_type"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "profile_occupancy")
            '''

        # Occupancy patterns and schedules
        elif "occupancy" in description_lower and ("pattern" in description_lower or "schedule" in description_lower or "when" in description_lower):
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> map(fn: (r) => ({{ r with hour_of_day: uint(v: time.hour(t: r._time)) }}))
              |> group(columns: ["hour_of_day", "room"])
              |> mean()
              |> yield(name: "occupancy_patterns")
            '''

        # Predictive occupancy analysis
        elif ("predict" in description_lower or "forecast" in description_lower or "tomorrow" in description_lower or "future" in description_lower) and "occupancy" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -14d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room", "profile_type"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> sort(columns: ["_time"])
              |> yield(name: "historical_occupancy_for_prediction")
            '''

        # General occupancy queries
        elif "occupancy" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "occupancy_rate")
            '''

        # Sensor data count or statistics
        elif "sensor" in description_lower and ("count" in description_lower or "data" in description_lower):
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> group(columns: ["_measurement"])
              |> count()
              |> yield(name: "sensor_data_count")
            '''

        # Default query for general time series questions
        else:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> group(columns: ["_measurement"])
              |> count()
              |> yield(name: "data_summary")
            '''

    def _format_neo4j_results(self, results: List[Dict]) -> str:
        """Format Neo4j results for LLM"""
        if not results:
            return "No data found in the graph database."

        formatted = "Graph database results:\n"
        for result in results[:10]:  # Limit results
            formatted += f"- {result}\n"
        return formatted

    def _format_influxdb_results(self, results: List[Dict]) -> str:
        """Format InfluxDB results for LLM"""
        if not results:
            return "No data found in the time series database."

        # Group results by measurement type for better analysis
        temp_data = [r for r in results if r.get('_measurement') == 'temperature']
        occupancy_data = [r for r in results if r.get('_measurement') == 'occupancy']

        formatted = "Time series database results:\n"

        if temp_data:
            # Calculate averages for temperature data, grouped by side if available
            room_temps = {}
            side_temps = {"sunny": [], "shaded": []}

            for result in temp_data:
                room = result.get('room', 'Unknown')
                temp = result.get('_value', 0)
                side = result.get('side', 'unknown')

                if room not in room_temps:
                    room_temps[room] = []
                room_temps[room].append(temp)

                if side in side_temps:
                    side_temps[side].append(temp)

            formatted += "Temperature data:\n"
            for room, temps in room_temps.items():
                avg_temp = sum(temps) / len(temps)
                formatted += f"- {room}: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

            # Add side-based analysis if side data is available
            if any(side_temps.values()):
                formatted += "\nTemperature by building side:\n"
                for side, temps in side_temps.items():
                    if temps:
                        avg_temp = sum(temps) / len(temps)
                        formatted += f"- {side.title()} side: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

        if occupancy_data:
            # Calculate occupancy rates
            room_occupancy = {}
            for result in occupancy_data:
                room = result.get('room', 'Unknown')
                occupied = result.get('_value', 0)
                if room not in room_occupancy:
                    room_occupancy[room] = []
                room_occupancy[room].append(occupied)

            formatted += "Occupancy data:\n"
            for room, occupancy in room_occupancy.items():
                rate = (sum(occupancy) / len(occupancy)) * 100
                formatted += f"- {room}: {rate:.0f}% occupancy rate ({len(occupancy)} readings)\n"

        return formatted




# Global instance
llm_manager = LLMManager()
