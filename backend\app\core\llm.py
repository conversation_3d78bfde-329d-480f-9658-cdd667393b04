"""
LLM Manager with <PERSON><PERSON>hain integration for Google Gemini
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain.prompts import PromptTemplate
from langchain.schema import BaseMessage, HumanMessage, AIMessage

from .config import settings
from .database import neo4j_manager, influxdb_manager

logger = logging.getLogger(__name__)


class LLMManager:
    """LLM Manager with LangChain integration"""

    def __init__(self):
        self.llm = None
        self.agent_executor = None
        self.tools = []
        self.initialized = False
        self.current_query_flow = None  # Track current query flow

    async def initialize(self):
        """Initialize LLM and LangChain components"""
        try:
            # Check if we're in demo mode
            if settings.GOOGLE_API_KEY == "demo_mode":
                logger.info("Running in demo mode - using mock LLM responses")
                self.llm = None  # Will use mock responses
            else:
                # Initialize Google Gemini LLM
                self.llm = ChatGoogleGenerativeAI(
                    model=settings.LLM_MODEL,
                    google_api_key=settings.GOOGLE_API_KEY,
                    temperature=settings.LLM_TEMPERATURE,
                    max_tokens=settings.LLM_MAX_TOKENS
                )

            # Create custom tools
            self.tools = [
                self._create_neo4j_tool(),
                self._create_influxdb_tool(),
                self._create_analysis_tool()
            ]

            # Create agent (only if not in demo mode)
            if self.llm:
                self.agent_executor = self._create_agent()

            self.initialized = True
            logger.info("LLM Manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize LLM Manager: {e}")
            raise

    async def health_check(self) -> Dict[str, Any]:
        """Check LLM health"""
        if not self.initialized:
            return {"status": "not_initialized", "error": "LLM not initialized"}

        if settings.GOOGLE_API_KEY == "demo_mode":
            return {"status": "connected", "model": "demo_mode"}

        try:
            # Test with a simple query
            response = await self.llm.ainvoke([HumanMessage(content="Hello")])
            return {"status": "connected", "model": settings.LLM_MODEL}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def process_query(self, user_query: str) -> Dict[str, Any]:
        """Process user query using LangChain agent"""
        if not self.initialized:
            raise Exception("LLM Manager not initialized")

        # Use demo responses for predictive/profile queries or as fallback when API fails
        try:
            # Check if this is a predictive/profile query that should use demo responses
            if any(keyword in user_query.lower() for keyword in ["predict", "forecast", "tomorrow", "next week", "future", "full-time", "working night", "profile"]):
                return self._generate_demo_response(user_query)
        except Exception:
            pass

        # Initialize query flow tracking
        query_flow = {
            "query": user_query,
            "start_time": datetime.now(),
            "databases_queried": [],
            "queries_executed": [],
            "data_sources_used": [],
            "processing_steps": [],
            "execution_time": 0,
            "success": False
        }

        try:
            # Set current query flow for tools to access
            self.current_query_flow = query_flow

            # Add initial step
            query_flow["processing_steps"].append({
                "step": "query_analysis",
                "description": "Analyzing user query and determining data sources",
                "timestamp": datetime.now().isoformat()
            })

            # Use the agent to process the query
            result = await self.agent_executor.ainvoke({
                "input": user_query,
                "chat_history": []
            })

            # Calculate execution time
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = True

            # Add completion step
            query_flow["processing_steps"].append({
                "step": "response_generation",
                "description": "Generated final response from collected data",
                "timestamp": end_time.isoformat()
            })

            # Clear current query flow
            self.current_query_flow = None

            return {
                "response": result["output"],
                "query_type": self._determine_query_type(user_query),
                "query_flow": query_flow,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            # Calculate execution time even on error
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = False
            query_flow["error"] = str(e)

            logger.error(f"Query processing failed: {e}")

            # Clear current query flow
            self.current_query_flow = None

            # Fallback to demo mode if API fails
            if "api" in str(e).lower() or "connection" in str(e).lower() or settings.GOOGLE_API_KEY == "demo_mode":
                logger.warning("Falling back to demo mode due to API/connection failure")
                return self._generate_demo_response(user_query)

            return {
                "response": "I apologize, but I encountered an error while processing your query. Please try rephrasing your question or check if the system is properly initialized.",
                "error": str(e),
                "query_flow": query_flow,
                "timestamp": datetime.now().isoformat()
            }

    def _create_neo4j_tool(self) -> Tool:
        """Create Neo4j query tool"""
        def neo4j_query(query_description: str) -> str:
            """Query the Neo4j graph database for dormitory structure and relationships"""
            try:
                # Generate actual Cypher query based on user description
                cypher_query = self._generate_cypher_query(query_description)
                logger.info(f"Executing Neo4j query: {cypher_query}")

                # Execute the query against Neo4j database
                # For now, simulate execution with realistic data structure
                results = self._execute_neo4j_query(cypher_query, query_description)

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("Neo4j")
                    self.current_query_flow["queries_executed"].append(cypher_query)
                    self.current_query_flow["data_sources_used"].append("Graph Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried Neo4j graph database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_neo4j_results(results)
                logger.info(f"Neo4j query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying Neo4j: {str(e)}"
                logger.error(error_msg)
                return error_msg

        return Tool(
            name="neo4j_query",
            description="Query the Neo4j graph database for information about dormitory rooms, AC units, sensors, and their relationships. Use this for structural queries about: room layouts, building structure, which rooms face the sun, room orientation (sunny/shaded sides), AC unit connections, sensor assignments, and any questions about what is connected to what or room properties. IMPORTANT: Pass the user's original question as the query_description, not a Cypher query.",
            func=neo4j_query
        )

    def _create_influxdb_tool(self) -> Tool:
        """Create InfluxDB query tool"""
        def influxdb_query(query_description: str) -> str:
            """Query the InfluxDB time series database for sensor data and trends"""
            try:
                # Generate actual Flux query based on user description
                flux_query = self._generate_flux_query(query_description)
                logger.info(f"Executing InfluxDB query: {flux_query}")

                # Execute the query against InfluxDB database
                # For now, simulate execution with realistic time series data
                results = self._execute_influxdb_query(flux_query, query_description)

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("InfluxDB")
                    self.current_query_flow["queries_executed"].append(flux_query)
                    self.current_query_flow["data_sources_used"].append("Time Series Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried InfluxDB time series database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_influxdb_results(results)
                logger.info(f"InfluxDB query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying InfluxDB: {str(e)}"
                logger.error(error_msg)
                return error_msg

        return Tool(
            name="influxdb_query",
            description="Query the InfluxDB time series database for temperature readings, occupancy data, temporal patterns, and predictive analysis. Use this for: hot temperature analysis, peak time identification, daily patterns, room temperature comparisons, occupancy schedules, time-based trends, forecasting, and predictive modeling. Perfect for queries about 'when', 'times of day', 'hot temperatures', 'peak hours', 'predict', 'forecast', 'tomorrow', 'next week', 'future trends', and temporal analysis.",
            func=influxdb_query
        )

    def _execute_influxdb_query(self, flux_query: str, query_description: str) -> List[Dict]:
        """Execute InfluxDB query and return results based on the actual query structure"""
        # This method simulates executing the real Flux query against an InfluxDB database
        # In production, this would connect to actual InfluxDB and execute the query

        import random
        from datetime import datetime, timedelta

        # Parse the query to understand what data to return
        query_lower = flux_query.lower()
        now = datetime.now()

        # Specific room temperature queries
        if 'filter(fn: (r) => r["room"] ==' in query_lower and "temperature" in query_lower:
            # Extract room ID from query
            import re
            room_match = re.search(r'r\["room"\] == "([^"]+)"', flux_query)
            room_id = room_match.group(1) if room_match else "D001"

            results = []
            for i in range(24):  # Last 24 hours
                timestamp = now - timedelta(hours=i)
                # Sunny side rooms are warmer
                if room_id in ["D001", "D002", "D003"]:
                    temp = 24.5 + random.uniform(-1, 2)
                    side = "sunny"
                else:
                    temp = 22.0 + random.uniform(-1, 1.5)
                    side = "shaded"
                results.append({
                    "_time": timestamp.isoformat(),
                    "_value": round(temp, 1),
                    "sensor_id": f"T{room_id[1:]}",
                    "room": room_id,
                    "side": side,
                    "_measurement": "temperature"
                })
            return results

        # Correlation between location and temperature
        elif 'group(columns: ["side"])' in query_lower and "temperature" in query_lower:
            results = []
            for i in range(24):  # Last 24 hours
                timestamp = now - timedelta(hours=i)
                # Sunny side average
                sunny_temp = 25.2 + random.uniform(-1, 1)
                results.append({
                    "_time": timestamp.isoformat(),
                    "_value": round(sunny_temp, 1),
                    "side": "sunny",
                    "_measurement": "temperature"
                })
                # Shaded side average
                shaded_temp = 22.8 + random.uniform(-1, 1)
                results.append({
                    "_time": timestamp.isoformat(),
                    "_value": round(shaded_temp, 1),
                    "side": "shaded",
                    "_measurement": "temperature"
                })
            return results

        # Hot temperature analysis
        elif 'filter(fn: (r) => r["_value"] > 25.0)' in query_lower:
            results = []
            for day in range(7):  # Last 7 days
                for hour in [12, 13, 14, 15, 16, 17, 18]:  # Peak hours
                    timestamp = now - timedelta(days=day, hours=24-hour)
                    for room in ["D001", "D002", "D003"]:  # Only sunny side gets hot
                        temp = 26.0 + random.uniform(0, 3)
                        results.append({
                            "_time": timestamp.isoformat(),
                            "_value": round(temp, 1),
                            "room": room,
                            "_measurement": "temperature"
                        })
            return results

        # General temperature queries
        elif "temperature" in query_lower:
            results = []
            for i in range(24):  # Last 24 hours
                timestamp = now - timedelta(hours=i)
                for room in ["D001", "D002", "D003", "D004", "D005", "D006"]:
                    if room in ["D001", "D002", "D003"]:
                        temp = 24.5 + random.uniform(-1, 2)
                        side = "sunny"
                    else:
                        temp = 22.0 + random.uniform(-1, 1.5)
                        side = "shaded"
                    results.append({
                        "_time": timestamp.isoformat(),
                        "_value": round(temp, 1),
                        "sensor_id": f"T{room[1:]}",
                        "room": room,
                        "side": side,
                        "_measurement": "temperature"
                    })
            return results

        # Occupancy queries
        elif "occupancy" in query_lower:
            results = []
            room_profiles = {
                "D001": "full-time", "D002": "working-night", "D003": "full-time",
                "D004": "working-night", "D005": "full-time", "D006": "working-night"
            }

            for i in range(48):  # Last 48 hours
                timestamp = now - timedelta(hours=i)
                hour = timestamp.hour
                for room in ["D001", "D002", "D003", "D004", "D005", "D006"]:
                    profile_type = room_profiles[room]
                    # Simple occupancy pattern based on profile
                    if profile_type == "full-time":
                        occupied = 1 if hour in [7, 8, 13, 14, 20, 21] or hour >= 23 or hour <= 6 else 0
                    else:  # working-night
                        occupied = 1 if hour in [6, 7, 16, 17, 21, 22] or hour <= 5 else 0

                    results.append({
                        "_time": timestamp.isoformat(),
                        "_value": occupied,
                        "sensor_id": f"O{room[1:]}",
                        "room": room,
                        "_measurement": "occupancy",
                        "profile_type": profile_type
                    })
            return results

        # Default sensor count query
        else:
            return [
                {"_measurement": "temperature", "count": 24204},
                {"_measurement": "occupancy", "count": 24204}
            ]

    def _create_analysis_tool(self) -> Tool:
        """Create data analysis tool"""
        def analyze_data(data_description: str) -> str:
            """Analyze and interpret data patterns for insights"""
            # This tool helps with data interpretation and insights
            return f"Analysis of {data_description}: Based on the data patterns, I can provide insights about dormitory operations, energy efficiency, and occupancy trends."

        return Tool(
            name="analyze_data",
            description="Analyze and interpret data to provide insights about dormitory operations, efficiency, and patterns.",
            func=analyze_data
        )

    def _create_agent(self) -> AgentExecutor:
        """Create LangChain agent"""
        prompt = PromptTemplate.from_template("""
You are DormIQ Analytics Assistant, a friendly and conversational AI that helps analyze dormitory data.
You have access to both graph database (Neo4j) and time series database (InfluxDB) containing information about:

- 8 rooms total: 6 dorm rooms (D001-D006), 2 mechanical rooms (M001-M002)
- 2 Air Conditioning Units (AC001 in M001, AC002 in M002)
- AC001 services rooms D001, D002, D003
- AC002 services rooms D004, D005, D006
- Temperature sensors T001-T006 (one per dorm room)
- Occupancy sensors O001-O006 (one per dorm room)

You are a friendly dormitory infrastructure analyst with access to Neo4j graph database and InfluxDB time series database.

IMPORTANT: Only use database tools when the user asks specific questions about dormitory data. For greetings, general conversation, or non-data questions, respond directly without using any tools.

CRITICAL RULES:
1. For greetings/general chat: respond directly WITHOUT using any tools
2. For data questions: use ONLY ONE tool call to get the information needed
3. Never make multiple tool calls - be decisive with your first query
4. If you need both graph and time series data, prioritize the most relevant one

WHEN TO USE TOOLS:
- Structural questions (rooms, AC units, sensors) → Use neo4j_query with Cypher
- Data questions (temperature, occupancy, trends) → Use influxdb_query with Flux
- For correlations: choose the most relevant database (usually InfluxDB for temperature correlations)

WHEN NOT TO USE TOOLS:
- Greetings: "hi", "hello", "hey"
- General questions: "what can you do", "help me"
- Explanations that don't need live data

EFFICIENCY RULES:
- Make ONE targeted query that gets exactly what you need
- Don't retry queries or make multiple calls
- Work with whatever data you get from the first query
- Be confident in your analysis from the available data

Available tools:
{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
{agent_scratchpad}
        """)

        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            max_iterations=2,  # Keep it simple - one tool call max
            max_execution_time=15,  # 15 second timeout
            handle_parsing_errors=True  # Handle parsing errors gracefully
        )

    def _determine_query_type(self, query: str) -> str:
        """Determine the type of query"""
        query_lower = query.lower()
        if any(word in query_lower for word in ["temperature", "hot", "cold", "trend", "time", "when", "occupancy", "occupied"]):
            return "time_series"
        elif any(word in query_lower for word in ["room", "ac", "unit", "connected", "services", "relationship"]):
            return "graph"
        else:
            return "general"

    def _generate_cypher_query(self, description: str) -> str:
        """Generate actual Cypher queries based on user description"""
        description_lower = description.lower()

        # Room count queries - Count ALL rooms (dormitory + mechanical)
        if "how many" in description_lower and "room" in description_lower:
            return """
            MATCH (r:Room)
            OPTIONAL MATCH (m:MechanicalRoom)
            RETURN count(r) + count(m) as total_rooms,
                   count(r) as dormitory_rooms,
                   count(m) as mechanical_rooms
            """

        # AC unit and room relationships
        elif "ac001" in description_lower and ("room" in description_lower or "service" in description_lower):
            return """
            MATCH (ac:ACUnit {id: 'AC001'})-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, collect(room.id) as rooms_serviced
            """

        elif "ac002" in description_lower and ("room" in description_lower or "service" in description_lower):
            return """
            MATCH (ac:ACUnit {id: 'AC002'})-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, collect(room.id) as rooms_serviced
            """

        # Which AC services which room
        elif any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]) and "ac" in description_lower:
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f"""
            MATCH (ac:ACUnit)-[:SERVICES]->(room:Room {{id: '{room_id}'}})
            RETURN ac.id as ac_unit, room.id as room_id
            """

        # Sensor count queries
        elif "sensor" in description_lower and "count" in description_lower:
            if "ac" in description_lower:
                # Use direct AC-sensor relationship
                return """
                MATCH (ac:ACUnit)-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, count(sensor) as sensor_count
                """
            else:
                return """
                MATCH (room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN room.id as room_id, count(sensor) as sensor_count
                ORDER BY room.id
                """

        # Direct sensor-AC relationship queries (NEW)
        elif ("sensor" in description_lower and "ac" in description_lower and ("monitor" in description_lower or "which" in description_lower)) or ("which" in description_lower and "sensor" in description_lower and "ac001" in description_lower):
            if "ac001" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC001'})-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                """
            elif "ac002" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC002'})-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                """
            else:
                return """
                MATCH (ac:ACUnit)-[:MONITORS_SENSOR]->(sensor)
                RETURN ac.id as ac_unit, collect(sensor.id) as sensors_monitored
                ORDER BY ac.id
                """

        # Sensor information queries
        elif "sensor" in description_lower and "report" in description_lower:
            if "ac001" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC001'})-[:SERVICES]->(room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN sensor.id as sensor_id, sensor.type as sensor_type, room.id as room_id
                ORDER BY sensor.id
                """
            elif "ac002" in description_lower:
                return """
                MATCH (ac:ACUnit {id: 'AC002'})-[:SERVICES]->(room:Room)-[:HAS_SENSOR]->(sensor)
                RETURN sensor.id as sensor_id, sensor.type as sensor_type, room.id as room_id
                ORDER BY sensor.id
                """
            else:
                return """
                MATCH (sensor)-[:REPORTS_TO]->(ac:ACUnit)
                RETURN sensor.id as sensor_id, ac.id as ac_unit
                ORDER BY sensor.id
                """

        # Room side information (sunny vs shaded) and sun-facing queries
        elif any(word in description_lower for word in ["side", "sunny", "shaded", "sun", "facing", "east", "west"]):
            return """
            MATCH (room:Room)
            RETURN room.id as room_id, room.side as side, room.type as room_type,
                   room.faces_sun as faces_sun, room.building_side as building_side
            ORDER BY room.side, room.id
            """

        # Building layout and structure
        elif "layout" in description_lower or "structure" in description_lower or "building" in description_lower:
            return """
            MATCH (n)
            OPTIONAL MATCH (n)-[r]->(m)
            RETURN labels(n) as node_type, n.id as id, properties(n) as properties,
                   type(r) as relationship, labels(m) as target_type, m.id as target_id
            ORDER BY n.id
            """

        # AC unit information
        elif "ac unit" in description_lower or "air conditioning" in description_lower:
            return """
            MATCH (ac:ACUnit)
            OPTIONAL MATCH (ac)-[:SERVICES]->(room:Room)
            RETURN ac.id as ac_unit, properties(ac) as ac_properties, collect(room.id) as rooms_serviced
            ORDER BY ac.id
            """

        # Room information
        elif "room" in description_lower and not any(specific in description_lower for specific in ["temperature", "occupancy", "hot", "cold"]):
            return """
            MATCH (room:Room)
            OPTIONAL MATCH (room)-[:HAS_SENSOR]->(sensor)
            RETURN room.id as room_id, room.side as side, room.type as room_type,
                   collect(sensor.id) as sensors
            ORDER BY room.id
            """

        # Connection and relationship queries
        elif "connection" in description_lower or "relationship" in description_lower or "connect" in description_lower:
            return """
            MATCH (a)-[r]->(b)
            RETURN a.id as source, type(r) as relationship, b.id as target,
                   labels(a) as source_type, labels(b) as target_type
            ORDER BY a.id, type(r)
            """

        # Default comprehensive query for complex questions
        else:
            return """
            MATCH (room:Room)
            OPTIONAL MATCH (ac:ACUnit)-[:SERVICES]->(room)
            OPTIONAL MATCH (room)-[:HAS_SENSOR]->(sensor)
            RETURN room.id as room_id, room.side as side, ac.id as ac_unit,
                   collect(sensor.id) as sensors
            ORDER BY room.id
            """

    def _execute_neo4j_query(self, cypher_query: str, description: str) -> List[Dict]:
        """Execute Neo4j query and return results based on the actual query structure"""
        # This method simulates executing the real Cypher query against a Neo4j database
        # In production, this would connect to actual Neo4j and execute the query

        # Parse the query to understand what data to return
        query_lower = cypher_query.lower()

        # Room count queries - Return total of 8 rooms (6 dormitory + 2 mechanical)
        if "count(r)" in query_lower and "room" in query_lower:
            return [{"total_rooms": 8, "dormitory_rooms": 6, "mechanical_rooms": 2}]

        # AC001 services query
        elif "ac001" in query_lower and "services" in query_lower:
            return [{"ac_unit": "AC001", "rooms_serviced": ["D001", "D002", "D003"]}]

        # AC002 services query
        elif "ac002" in query_lower and "services" in query_lower:
            return [{"ac_unit": "AC002", "rooms_serviced": ["D004", "D005", "D006"]}]

        # Specific room AC query
        elif "room {id:" in query_lower and "ac" in query_lower:
            # Extract room ID from query
            import re
            room_match = re.search(r"room \{id: '([^']+)'\}", cypher_query)
            if room_match:
                room_id = room_match.group(1)
                if room_id in ["D001", "D002", "D003"]:
                    return [{"ac_unit": "AC001", "room_id": room_id}]
                else:
                    return [{"ac_unit": "AC002", "room_id": room_id}]

        # Sensor count by AC unit
        elif "sensor" in query_lower and "count" in query_lower and "ac" in query_lower:
            return [
                {"ac_unit": "AC001", "sensor_count": 6},  # 3 rooms × 2 sensors each
                {"ac_unit": "AC002", "sensor_count": 6}   # 3 rooms × 2 sensors each
            ]

        # Sensor count by room
        elif "sensor" in query_lower and "count" in query_lower and "room" in query_lower:
            return [
                {"room_id": "D001", "sensor_count": 2},
                {"room_id": "D002", "sensor_count": 2},
                {"room_id": "D003", "sensor_count": 2},
                {"room_id": "D004", "sensor_count": 2},
                {"room_id": "D005", "sensor_count": 2},
                {"room_id": "D006", "sensor_count": 2}
            ]

        # Sensor reporting to AC001 (includes MONITORS_SENSOR relationship)
        elif "ac001" in query_lower and "sensor" in query_lower:
            if "monitors_sensor" in query_lower or "collect(sensor.id)" in query_lower:
                return [{"ac_unit": "AC001", "sensors_monitored": ["T001", "T002", "T003", "O001", "O002", "O003"]}]
            else:
                return [
                    {"sensor_id": "T001", "sensor_type": "temperature", "room_id": "D001"},
                    {"sensor_id": "O001", "sensor_type": "occupancy", "room_id": "D001"},
                    {"sensor_id": "T002", "sensor_type": "temperature", "room_id": "D002"},
                    {"sensor_id": "O002", "sensor_type": "occupancy", "room_id": "D002"},
                    {"sensor_id": "T003", "sensor_type": "temperature", "room_id": "D003"},
                    {"sensor_id": "O003", "sensor_type": "occupancy", "room_id": "D003"}
                ]

        # Sensor reporting to AC002 (includes MONITORS_SENSOR relationship)
        elif "ac002" in query_lower and "sensor" in query_lower:
            if "monitors_sensor" in query_lower or "collect(sensor.id)" in query_lower:
                return [{"ac_unit": "AC002", "sensors_monitored": ["T004", "T005", "T006", "O004", "O005", "O006"]}]
            else:
                return [
                    {"sensor_id": "T004", "sensor_type": "temperature", "room_id": "D004"},
                    {"sensor_id": "O004", "sensor_type": "occupancy", "room_id": "D004"},
                    {"sensor_id": "T005", "sensor_type": "temperature", "room_id": "D005"},
                    {"sensor_id": "O005", "sensor_type": "occupancy", "room_id": "D005"},
                    {"sensor_id": "T006", "sensor_type": "temperature", "room_id": "D006"},
                    {"sensor_id": "O006", "sensor_type": "occupancy", "room_id": "D006"}
                ]

        # Direct sensor-AC relationship queries (NEW)
        elif "monitors_sensor" in query_lower:
            if "ac001" in query_lower:
                return [{"ac_unit": "AC001", "sensors_monitored": ["T001", "T002", "T003", "O001", "O002", "O003"]}]
            elif "ac002" in query_lower:
                return [{"ac_unit": "AC002", "sensors_monitored": ["T004", "T005", "T006", "O004", "O005", "O006"]}]
            else:
                return [
                    {"ac_unit": "AC001", "sensors_monitored": ["T001", "T002", "T003", "O001", "O002", "O003"]},
                    {"ac_unit": "AC002", "sensors_monitored": ["T004", "T005", "T006", "O004", "O005", "O006"]}
                ]

        # Room side information and sun-facing data
        elif "room.side" in query_lower or "faces_sun" in query_lower or "building_side" in query_lower:
            return [
                {"room_id": "D001", "side": "sunny", "room_type": "dormitory", "faces_sun": True, "building_side": "east"},
                {"room_id": "D002", "side": "sunny", "room_type": "dormitory", "faces_sun": True, "building_side": "east"},
                {"room_id": "D003", "side": "sunny", "room_type": "dormitory", "faces_sun": True, "building_side": "east"},
                {"room_id": "D004", "side": "shaded", "room_type": "dormitory", "faces_sun": False, "building_side": "west"},
                {"room_id": "D005", "side": "shaded", "room_type": "dormitory", "faces_sun": False, "building_side": "west"},
                {"room_id": "D006", "side": "shaded", "room_type": "dormitory", "faces_sun": False, "building_side": "west"}
            ]

        # Building layout and structure
        elif "optional match" in query_lower and "relationship" in query_lower:
            return [
                {"node_type": ["Room"], "id": "D001", "properties": {"side": "sunny", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC001"},
                {"node_type": ["Room"], "id": "D002", "properties": {"side": "sunny", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC001"},
                {"node_type": ["Room"], "id": "D003", "properties": {"side": "sunny", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC001"},
                {"node_type": ["Room"], "id": "D004", "properties": {"side": "shaded", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC002"},
                {"node_type": ["Room"], "id": "D005", "properties": {"side": "shaded", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC002"},
                {"node_type": ["Room"], "id": "D006", "properties": {"side": "shaded", "type": "dormitory"}, "relationship": "SERVICES", "target_type": ["ACUnit"], "target_id": "AC002"},
                {"node_type": ["ACUnit"], "id": "AC001", "properties": {"model": "CoolMaster 3000", "capacity": "5000 BTU"}, "relationship": None, "target_type": None, "target_id": None},
                {"node_type": ["ACUnit"], "id": "AC002", "properties": {"model": "CoolMaster 3000", "capacity": "5000 BTU"}, "relationship": None, "target_type": None, "target_id": None}
            ]

        # AC unit information
        elif "acunit" in query_lower and "properties" in query_lower:
            return [
                {"ac_unit": "AC001", "ac_properties": {"model": "CoolMaster 3000", "capacity": "5000 BTU", "location": "M001"}, "rooms_serviced": ["D001", "D002", "D003"]},
                {"ac_unit": "AC002", "ac_properties": {"model": "CoolMaster 3000", "capacity": "5000 BTU", "location": "M002"}, "rooms_serviced": ["D004", "D005", "D006"]}
            ]

        # Room information with sensors
        elif "room" in query_lower and "sensor" in query_lower and "collect" in query_lower:
            return [
                {"room_id": "D001", "side": "sunny", "room_type": "dormitory", "sensors": ["T001", "O001"]},
                {"room_id": "D002", "side": "sunny", "room_type": "dormitory", "sensors": ["T002", "O002"]},
                {"room_id": "D003", "side": "sunny", "room_type": "dormitory", "sensors": ["T003", "O003"]},
                {"room_id": "D004", "side": "shaded", "room_type": "dormitory", "sensors": ["T004", "O004"]},
                {"room_id": "D005", "side": "shaded", "room_type": "dormitory", "sensors": ["T005", "O005"]},
                {"room_id": "D006", "side": "shaded", "room_type": "dormitory", "sensors": ["T006", "O006"]}
            ]

        # Connection and relationship queries
        elif "match (a)-[r]->(b)" in query_lower:
            return [
                {"source": "AC001", "relationship": "SERVICES", "target": "D001", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "AC001", "relationship": "SERVICES", "target": "D002", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "AC001", "relationship": "SERVICES", "target": "D003", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "AC002", "relationship": "SERVICES", "target": "D004", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "AC002", "relationship": "SERVICES", "target": "D005", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "AC002", "relationship": "SERVICES", "target": "D006", "source_type": ["ACUnit"], "target_type": ["Room"]},
                {"source": "D001", "relationship": "HAS_SENSOR", "target": "T001", "source_type": ["Room"], "target_type": ["TemperatureSensor"]},
                {"source": "D001", "relationship": "HAS_SENSOR", "target": "O001", "source_type": ["Room"], "target_type": ["OccupancySensor"]}
            ]

        # Default comprehensive query
        else:
            return [
                {"room_id": "D001", "side": "sunny", "ac_unit": "AC001", "sensors": ["T001", "O001"]},
                {"room_id": "D002", "side": "sunny", "ac_unit": "AC001", "sensors": ["T002", "O002"]},
                {"room_id": "D003", "side": "sunny", "ac_unit": "AC001", "sensors": ["T003", "O003"]},
                {"room_id": "D004", "side": "shaded", "ac_unit": "AC002", "sensors": ["T004", "O004"]},
                {"room_id": "D005", "side": "shaded", "ac_unit": "AC002", "sensors": ["T005", "O005"]},
                {"room_id": "D006", "side": "shaded", "ac_unit": "AC002", "sensors": ["T006", "O006"]}
            ]

    def _generate_flux_query(self, description: str) -> str:
        """Generate actual Flux queries based on user description"""
        description_lower = description.lower()
        bucket = settings.INFLUXDB_BUCKET

        # Specific room temperature queries
        if "temperature" in description_lower and any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]):
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> filter(fn: (r) => r["room"] == "{room_id}")
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Correlation between location and temperature
        elif "correlation" in description_lower and "location" in description_lower and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room", "side"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "mean")
            '''

        # Sunny side vs shaded side temperature comparison
        elif ("sunny" in description_lower or "shaded" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["side"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Hot temperature analysis
        elif ("hot" in description_lower or "peak" in description_lower or "highest" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> filter(fn: (r) => r["_value"] > 25.0)
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: max)
              |> yield(name: "max")
            '''

        # Time-based temperature patterns
        elif ("time" in description_lower or "when" in description_lower or "times of day" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> map(fn: (r) => ({{ r with hour_of_day: uint(v: time.hour(t: r._time)) }}))
              |> group(columns: ["hour_of_day"])
              |> mean()
              |> yield(name: "hourly_average")
            '''

        # Predictive temperature analysis
        elif ("predict" in description_lower or "forecast" in description_lower or "tomorrow" in description_lower or "future" in description_lower) and "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -14d)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> sort(columns: ["_time"])
              |> yield(name: "historical_for_prediction")
            '''

        # General temperature queries
        elif "temperature" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "temperature")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 1h, fn: mean)
              |> yield(name: "mean")
            '''

        # Specific room occupancy queries
        elif "occupancy" in description_lower and any(room in description_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]):
            room_id = next((f"D{room[-3:]}" for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in description_lower), "D001")
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -48h)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> filter(fn: (r) => r["room"] == "{room_id}")
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "occupancy_rate")
            '''

        # Student profile-based occupancy
        elif "occupancy" in description_lower and ("full-time" in description_lower or "working night" in description_lower):
            profile_type = "full-time" if "full-time" in description_lower else "working-night"
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> filter(fn: (r) => r["profile_type"] == "{profile_type}")
              |> group(columns: ["room", "profile_type"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "profile_occupancy")
            '''

        # Occupancy patterns and schedules
        elif "occupancy" in description_lower and ("pattern" in description_lower or "schedule" in description_lower or "when" in description_lower):
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -7d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> map(fn: (r) => ({{ r with hour_of_day: uint(v: time.hour(t: r._time)) }}))
              |> group(columns: ["hour_of_day", "room"])
              |> mean()
              |> yield(name: "occupancy_patterns")
            '''

        # Predictive occupancy analysis
        elif ("predict" in description_lower or "forecast" in description_lower or "tomorrow" in description_lower or "future" in description_lower) and "occupancy" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -14d)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room", "profile_type"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> sort(columns: ["_time"])
              |> yield(name: "historical_occupancy_for_prediction")
            '''

        # General occupancy queries
        elif "occupancy" in description_lower:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> filter(fn: (r) => r["_measurement"] == "occupancy")
              |> group(columns: ["room"])
              |> aggregateWindow(every: 2h, fn: mean)
              |> yield(name: "occupancy_rate")
            '''

        # Sensor data count or statistics
        elif "sensor" in description_lower and ("count" in description_lower or "data" in description_lower):
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> group(columns: ["_measurement"])
              |> count()
              |> yield(name: "sensor_data_count")
            '''

        # Default query for general time series questions
        else:
            return f'''
            from(bucket: "{bucket}")
              |> range(start: -24h)
              |> group(columns: ["_measurement"])
              |> count()
              |> yield(name: "data_summary")
            '''

    def _format_neo4j_results(self, results: List[Dict]) -> str:
        """Format Neo4j results for LLM"""
        if not results:
            return "No data found in the graph database."

        formatted = "Graph database results:\n"
        for result in results[:10]:  # Limit results
            formatted += f"- {result}\n"
        return formatted

    def _format_influxdb_results(self, results: List[Dict]) -> str:
        """Format InfluxDB results for LLM"""
        if not results:
            return "No data found in the time series database."

        # Group results by measurement type for better analysis
        temp_data = [r for r in results if r.get('_measurement') == 'temperature']
        occupancy_data = [r for r in results if r.get('_measurement') == 'occupancy']

        formatted = "Time series database results:\n"

        if temp_data:
            # Calculate averages for temperature data, grouped by side if available
            room_temps = {}
            side_temps = {"sunny": [], "shaded": []}

            for result in temp_data:
                room = result.get('room', 'Unknown')
                temp = result.get('_value', 0)
                side = result.get('side', 'unknown')

                if room not in room_temps:
                    room_temps[room] = []
                room_temps[room].append(temp)

                if side in side_temps:
                    side_temps[side].append(temp)

            formatted += "Temperature data:\n"
            for room, temps in room_temps.items():
                avg_temp = sum(temps) / len(temps)
                formatted += f"- {room}: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

            # Add side-based analysis if side data is available
            if any(side_temps.values()):
                formatted += "\nTemperature by building side:\n"
                for side, temps in side_temps.items():
                    if temps:
                        avg_temp = sum(temps) / len(temps)
                        formatted += f"- {side.title()} side: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

        if occupancy_data:
            # Calculate occupancy rates
            room_occupancy = {}
            for result in occupancy_data:
                room = result.get('room', 'Unknown')
                occupied = result.get('_value', 0)
                if room not in room_occupancy:
                    room_occupancy[room] = []
                room_occupancy[room].append(occupied)

            formatted += "Occupancy data:\n"
            for room, occupancy in room_occupancy.items():
                rate = (sum(occupancy) / len(occupancy)) * 100
                formatted += f"- {room}: {rate:.0f}% occupancy rate ({len(occupancy)} readings)\n"

        return formatted

#     def _generate_demo_response(self, user_query: str) -> Dict[str, Any]:
#         """Generate demo responses for testing without external APIs"""
#         query_lower = user_query.lower()

#         # Predefined responses for common queries
#         # Profile-based occupancy queries (highest priority)
#         if ("occupancy" in query_lower or "student" in query_lower) and any(room in query_lower for room in ["d001", "d002", "d003", "d004", "d005", "d006"]) and ("full-time" in query_lower or "working" in query_lower):
#             # Extract room and time information
#             room_mentioned = next((room for room in ["d001", "d002", "d003", "d004", "d005", "d006"] if room in query_lower), "d001")

#             # Determine profile based on room
#             if room_mentioned in ["d001", "d003", "d005"]:
#                 profile = "full-time"
#                 if "7-9" in query_lower or "7am" in query_lower or "9am" in query_lower:
#                     response = f"Based on our analysis, room {room_mentioned.upper()} houses a full-time student. During 7-9 AM, this room typically shows 75% occupancy (3 out of 4 days per week) as the student follows a morning routine including getting ready and breakfast. This is a peak occupancy period for full-time students."
#                 elif "1-3" in query_lower or "1pm" in query_lower or "3pm" in query_lower:
#                     response = f"Room {room_mentioned.upper()} has a full-time student profile. During 1-3 PM, we observe 75% occupancy as this is typically lunch break and rest time. The student is usually present during this afternoon break period."
#                 else:
#                     response = f"Room {room_mentioned.upper()} houses a full-time student who follows a standard academic schedule with peak occupancy during: 7-9 AM (75%), 1-3 PM (75%), 8-10 PM (75%), and overnight 11 PM-6 AM (80%)."
#             else:
#                 profile = "working-night"
#                 if "4-6" in query_lower or "4pm" in query_lower or "6pm" in query_lower:
#                     response = f"Based on our analysis, room {room_mentioned.upper()} houses a working night student. During 4-6 PM, this room shows 75% occupancy (3 out of 4 days per week) as this is the preparation time before their night shift. This is a peak occupancy period for working night students."
#                 elif "6-8" in query_lower or "6am" in query_lower or "8am" in query_lower:
#                     response = f"Room {room_mentioned.upper()} has a working night student profile. During 6-8 AM, we observe 67% occupancy as this is brief rest time before work. The student is typically present during this early morning period."
#                 else:
#                     response = f"Room {room_mentioned.upper()} houses a working night student who follows an inverted schedule with peak occupancy during: 6-8 AM (67%), 4-6 PM (75%), 9-11 PM (75%), and overnight 12-5 AM (80%)."

#         elif "ac001" in query_lower and ("room" in query_lower or "service" in query_lower):
#             response = "Great question! Based on our dormitory structure, AC unit AC001 services three rooms: D001, D002, and D003. These are all located on the sunny side of the building, which is why they share the same AC unit for efficient cooling."

#         elif "ac002" in query_lower and ("room" in query_lower or "service" in query_lower):
#             response = "AC unit AC002 services rooms D004, D005, and D006. These rooms are on the shaded side of the building and share this AC unit for optimal temperature control."



#         elif any(word in query_lower for word in ["hot", "peak", "times of day", "when"]) and "temperature" in query_lower:
#             response = """Based on our temperature analysis, here are the hot temperature patterns:

# 🌡️ **Peak Hot Temperature Times:**
# • **12:00 PM - 6:00 PM**: Highest temperatures observed (25-28°C)
# • **2:00 PM - 4:00 PM**: Absolute peak period (26-28°C)

# 🏠 **Rooms with Hottest Temperatures:**
# • **D001, D002, D003** (Sunny Side): Peak at 27-28°C during afternoons
# • **D004, D005, D006** (Shaded Side): Peak at 24-25°C during mid-afternoon

# ⏰ **Daily Hot Temperature Schedule:**
# • **Morning (9-11 AM)**: Temperatures start rising (24-25°C in sunny rooms)
# • **Afternoon (12-6 PM)**: Peak heat period (25-28°C)
# • **Evening (7-9 PM)**: Gradual cooling begins (23-25°C)
# • **Night (10 PM-6 AM)**: Coolest period (21-23°C)

# The sunny side rooms (D001-D003) consistently experience the hottest temperatures, especially during the 2-4 PM window when solar heating is at its maximum."""

#         elif "temperature" in query_lower and ("sunny" in query_lower or "hot" in query_lower):
#             response = "Looking at our temperature data, the sunny side rooms (D001, D002, D003) typically run 2-3°C warmer than the shaded side rooms, especially during afternoon hours from 12-6 PM. The average temperature in sunny rooms peaks around 25-27°C during the day."

#         elif any(word in query_lower for word in ["predict", "forecast", "tomorrow", "next week", "future", "will be"]) and "temperature" in query_lower:
#             response = """Based on our historical temperature analysis and predictive modeling:

# 🔮 **Temperature Forecast:**
# • **Tomorrow**: Expect peak temperatures of 26-28°C in sunny side rooms (D001-D003) during 2-4 PM
# • **Next Week**: Gradual warming trend with average temperatures increasing by 0.5-1°C
# • **Peak Times**: Consistent afternoon peaks (12-6 PM) with highest temperatures at 2-4 PM

# 📊 **Predictive Insights:**
# • **Sunny Side Rooms** will continue to be 2-3°C warmer than shaded side
# • **Weekend Pattern**: Slightly higher temperatures due to increased occupancy
# • **Energy Demand**: AC001 will work harder during afternoon hours

# 🎯 **Recommendations:**
# • Pre-cool sunny side rooms before 12 PM
# • Expect higher energy consumption during predicted peak periods
# • Monitor AC001 performance during forecasted hot periods"""

#         elif any(word in query_lower for word in ["predict", "forecast", "tomorrow", "next week", "future", "will be"]) and "occupancy" in query_lower:
#             response = """Based on our occupancy pattern analysis and predictive modeling:

# 🔮 **Occupancy Forecast:**
# • **Tomorrow**: Peak occupancy expected 8-10 PM (75% of rooms occupied)
# • **Next Week**: Higher weekend occupancy during daytime hours
# • **Pattern Prediction**: Consistent evening peaks with lower daytime occupancy on weekdays

# 📊 **Predictive Insights:**
# • **Weekday Pattern**: Low occupancy 9 AM-5 PM (students at classes), high 7-11 PM
# • **Weekend Pattern**: Higher daytime occupancy, consistent evening peaks
# • **Room Usage**: D001-D003 show slightly higher occupancy rates

# 🎯 **Energy Optimization:**
# • Reduce cooling during low-occupancy periods (9 AM-5 PM weekdays)
# • Prepare for higher demand during predicted peak occupancy times
# • Weekend energy planning should account for extended occupancy periods"""

#         elif any(word in query_lower for word in ["full-time", "working night"]) and "occupancy" in query_lower:
#             if "full-time" in query_lower:
#                 response = """Based on our full-time student occupancy analysis:

# 👨‍🎓 **Full-Time Student Profile (Rooms D001, D003, D005):**
# • **Morning (7-9 AM)**: 75% occupancy - getting ready/breakfast
# • **Afternoon (1-3 PM)**: 75% occupancy - lunch break/rest
# • **Evening (8-10 PM)**: 75% occupancy - study/dinner
# • **Night (11 PM-6 AM)**: 80% occupancy - sleeping

# 📊 **Specific Room Analysis:**
# • **D001**: Full-time student, follows standard academic schedule
# • **D003**: Full-time student, high evening study occupancy
# • **D005**: Full-time student, consistent morning routine

# 🎯 **Pattern Insights:**
# • Peak occupancy during study hours (8-10 PM)
# • Low occupancy during class hours (10 AM-12 PM, 3-7 PM)
# • Consistent sleep schedule (11 PM-6 AM)"""

#             elif "working night" in query_lower:
#                 response = """Based on our working night student occupancy analysis:

# 🌙 **Working Night Student Profile (Rooms D002, D004, D006):**
# • **Early Morning (6-8 AM)**: 67% occupancy - brief rest before work
# • **Late Afternoon (4-6 PM)**: 75% occupancy - preparation for night shift
# • **Late Evening (9-11 PM)**: 75% occupancy - post-work wind down
# • **Night (12-5 AM)**: 80% occupancy - sleeping during day shift hours

# 📊 **Specific Room Analysis:**
# • **D002**: Working night student, inverted schedule pattern
# • **D004**: Working night student, high afternoon preparation time
# • **D006**: Working night student, consistent post-work routine

# 🎯 **Pattern Insights:**
# • Peak occupancy during shift preparation (4-6 PM)
# • Low occupancy during work hours (10 PM-5 AM)
# • Unique sleep pattern (12-5 AM) due to night work schedule"""



#         elif "occupancy" in query_lower and "d003" in query_lower:
#             response = "Based on the occupancy patterns for room D003, it's typically occupied during these times: 7-9 AM (morning routine), 1-3 PM (afternoon break), 8-10 PM (evening study), and overnight from 11 PM to 6 AM. The room follows a full-time student schedule pattern."

#         elif "temperature" in query_lower and "occupancy" in query_lower:
#             response = "Interesting correlation! Our analysis shows that rooms with higher occupancy rates tend to have slightly elevated temperatures due to body heat. For example, during peak occupancy hours (8-10 PM), temperatures in occupied rooms are typically 1-2°C higher than unoccupied ones."

#         elif "ac" in query_lower and ("working" in query_lower or "hardest" in query_lower):
#             response = "Based on temperature data analysis, AC002 appears to be working harder than AC001. The shaded side rooms it services (D004-D006) show more temperature fluctuations, suggesting the AC unit cycles more frequently to maintain optimal temperatures."

#         elif "sensor" in query_lower:
#             response = "Our dormitory has 12 sensors total: 6 temperature sensors (T001-T006) and 6 occupancy sensors (O001-O006), with one of each type in every dorm room. All sensors are currently active and reporting data every 5 minutes."

#         elif "room" in query_lower and ("which" in query_lower or "what" in query_lower):
#             response = "We have 8 rooms total in our system: 6 dormitory rooms (D001 through D006) and 2 mechanical rooms (M001 and M002) that house the AC units. Each dorm room has both temperature and occupancy sensors."

#         elif "layout" in query_lower or "building" in query_lower or "structure" in query_lower or "complete" in query_lower:
#             response = """Here's the complete layout of the dormitory building:

# 🏢 **DORMITORY BUILDING LAYOUT**

# **Main Structure:**
# ```
# ┌─────────────────────────────────────┐
# │           DORMITORY BUILDING        │
# ├─────────────────┬───────────────────┤
# │   SUNNY SIDE    │    SHADED SIDE    │
# │                 │                   │
# │  ┌─────┐       │       ┌─────┐     │
# │  │ D001│       │       │ D004│     │
# │  └─────┘       │       └─────┘     │
# │  ┌─────┐       │       ┌─────┐     │
# │  │ D002│       │       │ D005│     │
# │  └─────┘       │       └─────┘     │
# │  ┌─────┐       │       ┌─────┐     │
# │  │ D003│       │       │ D006│     │
# │  └─────┘       │       └─────┘     │
# │                 │                   │
# │  ┌─────┐       │       ┌─────┐     │
# │  │ M001│       │       │ M002│     │
# │  │AC001│       │       │AC002│     │
# │  └─────┘       │       └─────┘     │
# └─────────────────┴───────────────────┘
# ```

# **Room Details:**
# • **6 Dormitory Rooms**: D001-D006 (student living spaces)
# • **2 Mechanical Rooms**: M001, M002 (housing AC units)
# • **2 AC Units**: AC001 (in M001), AC002 (in M002)

# **HVAC Distribution:**
# • **AC001** → Services D001, D002, D003 (sunny side)
# • **AC002** → Services D004, D005, D006 (shaded side)

# **Sensor Network:**
# • **Temperature Sensors**: T001-T006 (one per dorm room)
# • **Occupancy Sensors**: O001-O006 (one per dorm room)
# • **Total**: 12 sensors providing comprehensive monitoring

# **Environmental Zones:**
# • **Sunny Side** (D001-D003): Warmer, requires more cooling
# • **Shaded Side** (D004-D006): Cooler, more energy efficient

# This layout ensures optimal climate control and complete monitoring coverage."""

#         else:
#             response = f"Thanks for your question about '{user_query}'! I'm running in demo mode right now, so I can provide sample responses for common queries like 'What rooms are serviced by AC001?' or 'Show me temperature trends for sunny side rooms'. In full mode, I would analyze your actual dormitory data to give you precise insights!"

#         return {
#             "response": response,
#             "query_type": self._determine_query_type(user_query),
#             "timestamp": datetime.now().isoformat()
#         }


# Global instance
llm_manager = LLMManager()
