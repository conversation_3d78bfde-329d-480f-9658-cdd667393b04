"""
LLM Manager with <PERSON><PERSON>hain integration for Google Gemini
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain.prompts import PromptTemplate
from langchain.schema import BaseMessage, HumanMessage, AIMessage

from .config import settings
from .database import neo4j_manager, influxdb_manager

logger = logging.getLogger(__name__)


class LLMManager:
    """LLM Manager with LangChain integration"""

    def __init__(self):
        self.llm = None
        self.agent_executor = None
        self.tools = []
        self.initialized = False
        self.current_query_flow = None  # Track current query flow
        # Session-based conversation memory
        self.conversation_memory = {}  # session_id -> conversation history
        self.max_memory_length = 10  # Keep last 10 exchanges per session

    async def initialize(self):
        """Initialize LLM and LangChain components"""
        try:
            # Initialize Google Gemini LLM
            self.llm = ChatGoogleGenerativeAI(
                model=settings.LLM_MODEL,
                google_api_key=settings.GOOGLE_API_KEY,
                temperature=settings.LLM_TEMPERATURE,
                max_tokens=settings.LLM_MAX_TOKENS
            )

            # Create custom tools
            self.tools = [
                self._create_neo4j_tool(),
                self._create_influxdb_tool(),
                self._create_analysis_tool()
            ]

            # Create agent
            self.agent_executor = self._create_agent()

            self.initialized = True
            logger.info("LLM Manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize LLM Manager: {e}")
            raise

    async def health_check(self) -> Dict[str, Any]:
        """Check LLM health"""
        if not self.initialized:
            return {"status": "not_initialized", "error": "LLM not initialized"}



        try:
            # Test with a simple query
            response = await self.llm.ainvoke([HumanMessage(content="Hello")])
            return {"status": "connected", "model": settings.LLM_MODEL}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def _add_to_conversation_memory(self, session_id: str, user_query: str, response: str, query_type: str = None):
        """Add conversation exchange to memory"""
        if session_id not in self.conversation_memory:
            self.conversation_memory[session_id] = []

        # Add new exchange
        exchange = {
            "user": user_query,
            "assistant": response,
            "timestamp": datetime.now().isoformat()
        }

        # Add query type if provided
        if query_type:
            exchange["query_type"] = query_type

        self.conversation_memory[session_id].append(exchange)

        # Keep only last N exchanges
        if len(self.conversation_memory[session_id]) > self.max_memory_length:
            self.conversation_memory[session_id] = self.conversation_memory[session_id][-self.max_memory_length:]

        logger.info(f"💭 [MEMORY] Added conversation to session {session_id}, total exchanges: {len(self.conversation_memory[session_id])}")

    def _get_conversation_context(self, session_id: str) -> str:
        """Get conversation context for a session"""
        if session_id not in self.conversation_memory or not self.conversation_memory[session_id]:
            return ""

        context = "Previous conversation context:\n"
        for exchange in self.conversation_memory[session_id][-3:]:  # Last 3 exchanges for context
            context += f"User: {exchange['user']}\n"
            context += f"Assistant: {exchange['assistant'][:200]}...\n\n"  # Truncate long responses

        return context

    def _detect_follow_up_query(self, user_query: str, session_id: str) -> bool:
        """Detect if this is a follow-up query that references previous context"""
        follow_up_indicators = [
            "what about", "and", "also", "too", "as well", "similarly",
            "in comparison", "compared to", "versus", "vs", "instead",
            "that room", "that sensor", "those rooms", "the same", "it", "they"
        ]

        query_lower = user_query.lower()
        has_follow_up_words = any(indicator in query_lower for indicator in follow_up_indicators)
        has_conversation_history = session_id in self.conversation_memory and len(self.conversation_memory[session_id]) > 0

        return has_follow_up_words and has_conversation_history

    async def process_query(self, user_query: str, session_id: str = "default") -> Dict[str, Any]:
        """Process user query using LangChain agent"""
        if not self.initialized:
            raise Exception("LLM Manager not initialized")



        # Initialize query flow tracking
        query_flow = {
            "query": user_query,
            "start_time": datetime.now(),
            "databases_queried": [],
            "queries_executed": [],
            "data_sources_used": [],
            "processing_steps": [],
            "execution_time": 0,
            "success": False
        }

        try:
            logger.info(f"🚀 [QUERY] Starting query processing: '{user_query}'")
            logger.info(f"⏱️  [QUERY] Start Timestamp: {datetime.now().isoformat()}")

            # Set current query flow for tools to access
            self.current_query_flow = query_flow

            # Add initial step
            query_flow["processing_steps"].append({
                "step": "query_analysis",
                "description": "Analyzing user query and determining data sources",
                "timestamp": datetime.now().isoformat()
            })

            # Determine query type and route accordingly
            query_type = self._determine_query_type(user_query, session_id)
            logger.info(f"🎯 [QUERY] Query type determined: {query_type}")
            response_content = ""

            if query_type == "predictive":
                logger.info(f"🔮 [PREDICTIVE] Starting predictive analysis")

                # Get historical data from InfluxDB for prediction
                flux_query = await self._generate_predictive_flux_query(user_query)
                logger.info(f"🔍 [PREDICTIVE] Generated historical data query: {flux_query}")

                influxdb_start = datetime.now()
                historical_data = await influxdb_manager.execute_query(flux_query)
                influxdb_time = (datetime.now() - influxdb_start).total_seconds()
                logger.info(f"✅ [PREDICTIVE] Historical data retrieved in {influxdb_time:.3f}s, {len(historical_data)} records")

                # Debug logging for occupancy queries
                if "occupancy" in user_query.lower():
                    logger.info(f"🔍 [PREDICTIVE-DEBUG] Occupancy query executed: {flux_query}")
                    logger.info(f"🔍 [PREDICTIVE-DEBUG] Raw results count: {len(historical_data)}")
                    if historical_data:
                        logger.info(f"🔍 [PREDICTIVE-DEBUG] Sample result: {historical_data[0]}")
                    else:
                        logger.warning(f"🔍 [PREDICTIVE-DEBUG] No occupancy data found for query!")

                # Generate prediction based on historical patterns
                prediction_result = await self._generate_prediction(user_query, historical_data)

                # Record in query flow
                query_flow["databases_queried"].append("InfluxDB")
                query_flow["queries_executed"].append(flux_query)
                query_flow["data_sources_used"].append("Time Series Database (Historical)")

                response_content = prediction_result

            elif query_type == "multi_database":
                # Handle multi-database queries that need both Neo4j and InfluxDB
                query_flow["processing_steps"].append({
                    "step": "multi_database_query",
                    "description": "Executing multi-database query (Neo4j + InfluxDB)",
                    "timestamp": datetime.now().isoformat()
                })

                logger.info(f"🔄 [MULTI-DB] Starting multi-database query execution")

                # First, get structural data from Neo4j
                cypher_query = await self._generate_cypher_query(user_query)
                logger.info(f"🔍 [MULTI-DB] Generated Neo4j query: {cypher_query}")

                neo4j_start = datetime.now()
                neo4j_results = await neo4j_manager.execute_query(cypher_query)
                neo4j_time = (datetime.now() - neo4j_start).total_seconds()
                logger.info(f"✅ [MULTI-DB] Neo4j query completed in {neo4j_time:.3f}s, {len(neo4j_results)} results")

                neo4j_result = self._format_neo4j_results(neo4j_results)

                # Then, get time series data from InfluxDB
                flux_query = await self._generate_flux_query(user_query)
                logger.info(f"🔍 [MULTI-DB] Generated InfluxDB query: {flux_query}")

                influxdb_start = datetime.now()
                influxdb_results = await influxdb_manager.execute_query(flux_query)
                influxdb_time = (datetime.now() - influxdb_start).total_seconds()
                logger.info(f"✅ [MULTI-DB] InfluxDB query completed in {influxdb_time:.3f}s, {len(influxdb_results)} results")

                influxdb_result = self._format_influxdb_results(influxdb_results)

                # Record in query flow
                query_flow["databases_queried"].extend(["Neo4j", "InfluxDB"])
                query_flow["queries_executed"].extend([cypher_query, flux_query])
                query_flow["data_sources_used"].extend(["Graph Database", "Time Series Database"])

                # Get conversation context
                conversation_context = self._get_conversation_context(session_id)
                is_follow_up = self._detect_follow_up_query(user_query, session_id)

                # Generate combined response
                response_prompt = f"""
                Based on the following data from our dormitory monitoring system, answer the user's question: "{user_query}"

                {conversation_context}

                Structural Data (Neo4j): {neo4j_result}

                Time Series Data (InfluxDB): {influxdb_result}

                {"This appears to be a follow-up question. Please reference the previous conversation context when appropriate." if is_follow_up else ""}

                Provide a comprehensive response that combines both structural and temporal data to fully answer their question.
                """

                response = await self.llm.ainvoke(response_prompt)
                response_content = response.content

            elif query_type == "time_series":
                # For temperature/time-based queries, directly call InfluxDB tool
                query_flow["processing_steps"].append({
                    "step": "influxdb_query",
                    "description": "Querying InfluxDB for temperature/time data",
                    "timestamp": datetime.now().isoformat()
                })

                # Call InfluxDB directly using async method
                flux_query = await self._generate_flux_query(user_query)
                logger.info(f"Executing InfluxDB query: {flux_query}")

                influxdb_results = await influxdb_manager.execute_query(flux_query)
                logger.info(f"InfluxDB query returned {len(influxdb_results)} results")
                if len(influxdb_results) == 0:
                    logger.warning(f"No data returned from InfluxDB for query: {flux_query}")
                else:
                    logger.info(f"Sample result: {influxdb_results[0] if influxdb_results else 'None'}")

                influxdb_result = self._format_influxdb_results(influxdb_results)

                # Record in query flow
                query_flow["databases_queried"].append("InfluxDB")
                query_flow["queries_executed"].append(flux_query)
                query_flow["data_sources_used"].append("Time Series Database")

                # Get conversation context
                conversation_context = self._get_conversation_context(session_id)
                is_follow_up = self._detect_follow_up_query(user_query, session_id)

                # Generate response using LLM with the data and context
                response_prompt = f"""
                Based on the following temperature data from our dormitory monitoring system, answer the user's question: "{user_query}"

                {conversation_context}

                Data: {influxdb_result}

                {"This appears to be a follow-up question. Please reference the previous conversation context when appropriate." if is_follow_up else ""}

                Provide a helpful, conversational response that directly answers their question about temperature patterns and times of day.
                """

                response = await self.llm.ainvoke(response_prompt)
                response_content = response.content

            elif query_type == "graph":
                # For structural queries, call Neo4j tool
                query_flow["processing_steps"].append({
                    "step": "neo4j_query",
                    "description": "Querying Neo4j for structural data",
                    "timestamp": datetime.now().isoformat()
                })

                # Call Neo4j directly using async method
                cypher_query = await self._generate_cypher_query(user_query)
                logger.info(f"Executing Neo4j query: {cypher_query}")

                neo4j_results = await neo4j_manager.execute_query(cypher_query)
                neo4j_result = self._format_neo4j_results(neo4j_results)

                # Record in query flow
                query_flow["databases_queried"].append("Neo4j")
                query_flow["queries_executed"].append(cypher_query)
                query_flow["data_sources_used"].append("Graph Database")

                # Get conversation context
                conversation_context = self._get_conversation_context(session_id)
                is_follow_up = self._detect_follow_up_query(user_query, session_id)

                # Generate response using LLM with the data and context
                response_prompt = f"""
                Based on the following structural data from our dormitory system, answer the user's question: "{user_query}"

                {conversation_context}

                Data: {neo4j_result}

                {"This appears to be a follow-up question. Please reference the previous conversation context when appropriate." if is_follow_up else ""}

                Provide a helpful, conversational response that directly answers their question about the dormitory structure.
                """

                response = await self.llm.ainvoke(response_prompt)
                response_content = response.content

            else:
                # For greetings and general questions, respond directly
                response_content = "Hello! I'm your DormIQ assistant. I can help you analyze temperature data, occupancy patterns, and dormitory structure. What would you like to know?"

            # Create result object
            result = {"output": response_content}

            # Calculate execution time
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = True

            # Add completion step
            query_flow["processing_steps"].append({
                "step": "response_generation",
                "description": "Generated final response from collected data",
                "timestamp": end_time.isoformat()
            })

            # Add to conversation memory with query type
            self._add_to_conversation_memory(session_id, user_query, response_content, query_type)

            # Clear current query flow
            self.current_query_flow = None

            return {
                "response": result["output"],
                "query_type": self._determine_query_type(user_query, session_id),
                "query_flow": query_flow,
                "session_id": session_id,
                "conversation_context": len(self.conversation_memory.get(session_id, [])),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            # Calculate execution time even on error
            end_time = datetime.now()
            query_flow["execution_time"] = (end_time - query_flow["start_time"]).total_seconds()
            query_flow["success"] = False
            query_flow["error"] = str(e)

            logger.error(f"Query processing failed: {e}")

            # Clear current query flow
            self.current_query_flow = None

            # No fallback - raise the actual error for production debugging

            return {
                "response": "I apologize, but I encountered an error while processing your query. Please try rephrasing your question or check if the system is properly initialized.",
                "query_type": self._determine_query_type(user_query, session_id),
                "error": str(e),
                "query_flow": query_flow,
                "timestamp": datetime.now().isoformat()
            }

    def _create_neo4j_tool(self) -> Tool:
        """Create Neo4j query tool"""
        async def neo4j_query(query_description: str) -> str:
            """Query the Neo4j graph database for dormitory structure and relationships"""
            try:
                # Generate actual Cypher query based on user description
                cypher_query = self._generate_cypher_query(query_description)
                logger.info(f"🔍 [NEO4J] Generated Cypher Query: {cypher_query}")
                logger.info(f"⏱️  [NEO4J] Query Timestamp: {datetime.now().isoformat()}")

                # Execute the query against Neo4j database
                start_time = datetime.now()
                results = await neo4j_manager.execute_query(cypher_query)
                execution_time = (datetime.now() - start_time).total_seconds()

                logger.info(f"✅ [NEO4J] Query executed successfully in {execution_time:.3f}s")
                logger.info(f"📊 [NEO4J] Raw Results: {results}")
                logger.info(f"📈 [NEO4J] Result Count: {len(results)} records")

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("Neo4j")
                    self.current_query_flow["queries_executed"].append(cypher_query)
                    self.current_query_flow["data_sources_used"].append("Graph Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried Neo4j graph database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_neo4j_results(results)
                logger.info(f"Neo4j query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying Neo4j: {str(e)}"
                logger.error(error_msg)
                return error_msg

        # Create a sync wrapper that directly calls the database
        def sync_neo4j_query(query_description: str) -> str:
            try:
                # Generate actual Cypher query based on user description using async method
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    cypher_query = loop.run_until_complete(self._generate_cypher_query(query_description))
                    logger.info(f"🔍 [NEO4J-SYNC] Generated Cypher Query: {cypher_query}")
                    logger.info(f"⏱️  [NEO4J-SYNC] Query Timestamp: {datetime.now().isoformat()}")

                    # Execute the query against Neo4j database
                    start_time = datetime.now()
                    results = loop.run_until_complete(neo4j_manager.execute_query(cypher_query))
                    execution_time = (datetime.now() - start_time).total_seconds()

                    logger.info(f"✅ [NEO4J-SYNC] Query executed successfully in {execution_time:.3f}s")
                    logger.info(f"📊 [NEO4J-SYNC] Raw Results: {results}")
                    logger.info(f"📈 [NEO4J-SYNC] Result Count: {len(results)} records")
                finally:
                    loop.close()

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("Neo4j")
                    self.current_query_flow["queries_executed"].append(cypher_query)
                    self.current_query_flow["data_sources_used"].append("Graph Database")

                formatted_result = self._format_neo4j_results(results)
                logger.info(f"Neo4j query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying Neo4j: {str(e)}"
                logger.error(error_msg)
                return error_msg

        return Tool(
            name="neo4j_query",
            description="Query the Neo4j graph database for information about dormitory rooms, AC units, sensors, and their relationships. Use this for structural queries about: room layouts, building structure, which rooms face the sun, room orientation (sunny/shaded sides), AC unit connections, sensor assignments, and any questions about what is connected to what or room properties. IMPORTANT: Pass the user's original question as the query_description, not a Cypher query.",
            func=sync_neo4j_query
        )

    def _create_influxdb_tool(self) -> Tool:
        """Create InfluxDB query tool"""
        async def influxdb_query(query_description: str) -> str:
            """Query the InfluxDB time series database for sensor data and trends"""
            try:
                # Generate actual Flux query based on user description
                flux_query = self._generate_flux_query(query_description)
                logger.info(f"🔍 [INFLUXDB] Generated Flux Query: {flux_query}")
                logger.info(f"⏱️  [INFLUXDB] Query Timestamp: {datetime.now().isoformat()}")

                # Execute the query against InfluxDB database
                start_time = datetime.now()
                results = await influxdb_manager.execute_query(flux_query)
                execution_time = (datetime.now() - start_time).total_seconds()

                logger.info(f"✅ [INFLUXDB] Query executed successfully in {execution_time:.3f}s")
                logger.info(f"📊 [INFLUXDB] Raw Results: {results}")
                logger.info(f"📈 [INFLUXDB] Result Count: {len(results)} records")

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("InfluxDB")
                    self.current_query_flow["queries_executed"].append(flux_query)
                    self.current_query_flow["data_sources_used"].append("Time Series Database")
                    self.current_query_flow["processing_steps"].append({
                        "step": "database_query",
                        "description": f"Queried InfluxDB time series database: {query_description}",
                        "timestamp": datetime.now().isoformat()
                    })

                formatted_result = self._format_influxdb_results(results)
                logger.info(f"InfluxDB query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying InfluxDB: {str(e)}"
                logger.error(error_msg)
                return error_msg

        # Create a sync wrapper that directly calls the database
        def sync_influxdb_query(query_description: str) -> str:
            try:
                # Generate actual Flux query based on user description using async method
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    flux_query = loop.run_until_complete(self._generate_flux_query(query_description))
                    logger.info(f"🔍 [INFLUXDB-SYNC] Generated Flux Query: {flux_query}")
                    logger.info(f"⏱️  [INFLUXDB-SYNC] Query Timestamp: {datetime.now().isoformat()}")

                    # Execute the query against InfluxDB database
                    start_time = datetime.now()
                    results = loop.run_until_complete(influxdb_manager.execute_query(flux_query))
                    execution_time = (datetime.now() - start_time).total_seconds()

                    logger.info(f"✅ [INFLUXDB-SYNC] Query executed successfully in {execution_time:.3f}s")
                    logger.info(f"📊 [INFLUXDB-SYNC] Raw Results: {results}")
                    logger.info(f"📈 [INFLUXDB-SYNC] Result Count: {len(results)} records")
                finally:
                    loop.close()

                # Record query flow if tracking is active
                if hasattr(self, 'current_query_flow') and self.current_query_flow:
                    self.current_query_flow["databases_queried"].append("InfluxDB")
                    self.current_query_flow["queries_executed"].append(flux_query)
                    self.current_query_flow["data_sources_used"].append("Time Series Database")

                formatted_result = self._format_influxdb_results(results)
                logger.info(f"InfluxDB query returned {len(results)} results")
                return formatted_result
            except Exception as e:
                error_msg = f"Error querying InfluxDB: {str(e)}"
                logger.error(error_msg)
                return error_msg

        return Tool(
            name="influxdb_query",
            description="Query the InfluxDB time series database for temperature readings, occupancy data, temporal patterns, and predictive analysis. Use this for: hot temperature analysis, peak time identification, daily patterns, room temperature comparisons, occupancy schedules, time-based trends, forecasting, and predictive modeling. Perfect for queries about 'when', 'times of day', 'hot temperatures', 'peak hours', 'predict', 'forecast', 'tomorrow', 'next week', 'future trends', and temporal analysis.",
            func=sync_influxdb_query
        )



    def _create_analysis_tool(self) -> Tool:
        """Create data analysis tool"""
        def analyze_data(data_description: str) -> str:
            """Analyze and interpret data patterns for insights"""
            # This tool helps with data interpretation and insights
            return f"Analysis of {data_description}: Based on the data patterns, I can provide insights about dormitory operations, energy efficiency, and occupancy trends."

        return Tool(
            name="analyze_data",
            description="Analyze and interpret data to provide insights about dormitory operations, efficiency, and patterns.",
            func=analyze_data
        )

    def _create_agent(self) -> AgentExecutor:
        """Create LangChain agent"""
        prompt = PromptTemplate.from_template("""
You are DormIQ Analytics Assistant, a friendly and conversational AI that helps analyze dormitory data.
You have access to both graph database (Neo4j) and time series database (InfluxDB) containing information about:

- 8 rooms total: 6 dorm rooms (D001-D006), 2 mechanical rooms (M001-M002)
- 2 Air Conditioning Units (AC001 in M001, AC002 in M002)
- AC001 services rooms D001, D002, D003
- AC002 services rooms D004, D005, D006
- Temperature sensors T001-T006 (one per dorm room)
- Occupancy sensors O001-O006 (one per dorm room)

CRITICAL: You MUST use the database tools to answer ANY question about dormitory data, temperatures, occupancy, rooms, AC units, or sensors.

WHEN TO USE TOOLS (ALWAYS use tools for these):
- ANY temperature questions → Use influxdb_query
- ANY occupancy questions → Use influxdb_query
- ANY "hot temperature" or "times of day" questions → Use influxdb_query
- Room/AC unit/sensor structure questions → Use neo4j_query
- Building layout questions → Use neo4j_query

WHEN NOT TO USE TOOLS (respond directly):
- Simple greetings: "hi", "hello", "hey"
- General capability questions: "what can you do"

TOOL SELECTION:
- Temperature, occupancy, time-based data → influxdb_query
- Room structure, AC connections, building layout → neo4j_query
- Temperature and occupancy comparisons, daily temperature queries → Use both neo4j_query and influxdb_query

IMPORTANT: If a user asks about temperature data, hot temperatures, times of day, or any sensor data, you MUST use the influxdb_query tool. Do not provide responses without querying the database first.

Available tools:
{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
{agent_scratchpad}
        """)

        agent = create_react_agent(self.llm, self.tools, prompt)
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            max_iterations=4,  # Allow for multi-database queries: Neo4j + InfluxDB + response generation
            max_execution_time=20,  # 20 second timeout for complex queries
            handle_parsing_errors=True  # Handle parsing errors gracefully
        )

    def _determine_query_type(self, query: str, session_id: str = None) -> str:
        """Determine the type of query with session context support"""
        query_lower = query.lower()

        # Predictive/forecasting queries (special handling)
        predictive_patterns = [
            "predict", "forecast", "tomorrow", "next week", "future", "will be",
            "expected", "projection", "trend", "what.*will", "expect", "anticipate"
        ]

        # Check for predictive keywords
        has_predictive_words = any(pattern in query_lower for pattern in predictive_patterns)

        if has_predictive_words:
            return "predictive"

        # Enhanced follow-up query detection with session context
        if session_id and self._detect_follow_up_query(query, session_id):
            # Get the last query type from conversation history
            if session_id in self.conversation_memory:
                recent_queries = self.conversation_memory[session_id][-3:]
                if recent_queries:
                    # Look for the most recent query that had a specific type
                    for exchange in reversed(recent_queries):
                        if isinstance(exchange, dict) and exchange.get('query_type'):
                            last_query_type = exchange['query_type']
                            if last_query_type in ['time_series', 'graph', 'multi_database']:
                                # Check if the follow-up query contains room references or similar context
                                import re
                                room_pattern = r'\b[dD]\d{3}\b'  # Matches D001, D002, etc.
                                if (re.search(room_pattern, query) or
                                    any(word in query_lower for word in ['what about', 'and', 'also', 'room', 'that', 'it', 'them'])):
                                    return last_query_type

        # Multi-database query indicators (need both Neo4j + InfluxDB)
        multi_db_patterns = [
            # AC performance analysis
            ("ac001" in query_lower or "ac002" in query_lower) and any(word in query_lower for word in ["temperature", "average", "performance", "working", "harder"]),
            # Room-specific sensor data analysis with AC context
            ("room" in query_lower or any(f"d00{i}" in query_lower for i in range(1, 7))) and any(word in query_lower for word in ["temperature", "occupancy", "data", "reading"]) and any(word in query_lower for word in ["serviced", "service", "ac", "unit"]),
            # Building efficiency queries
            ("ac unit" in query_lower or "air conditioning" in query_lower) and any(word in query_lower for word in ["temperature", "performance", "efficiency", "working", "harder"]),
            # Sensor correlation queries
            ("sensor" in query_lower) and any(word in query_lower for word in ["temperature", "data", "reading"]) and any(word in query_lower for word in ["ac", "connected", "monitor"]),
            # Sun-facing room analysis with temperature/occupancy
            ("sun" in query_lower or "sunny" in query_lower or "shaded" in query_lower) and any(word in query_lower for word in ["temperature", "occupancy", "pattern"]),
            # Average temperature in rooms serviced by AC
            "average" in query_lower and "temperature" in query_lower and ("serviced" in query_lower or "service" in query_lower)
        ]

        # Check for multi-database patterns first
        if any(multi_db_patterns):
            return "multi_database"

        if any(word in query_lower for word in ["temperature", "hot", "cold", "trend", "time", "when", "occupancy", "occupied"]):
            return "time_series"
        elif any(word in query_lower for word in ["room", "ac", "unit", "connected", "services", "relationship"]):
            return "graph"
        else:
            return "general"

    async def _generate_cypher_query(self, description: str) -> str:
        """Generate Cypher query using LLM based on user description"""

        # Database schema information for the LLM
        schema_info = """
        Neo4j Database Schema:

        Nodes:
        - Room: {id, type: 'dormitory', side: 'sunny'|'shaded'}
        - MechanicalRoom: {id, type: 'mechanical'}
        - ACUnit: {id, model, capacity, location}
        - TemperatureSensor: {id, type: 'temperature', unit: 'celsius', accuracy}
        - OccupancySensor: {id, type: 'occupancy', technology: 'PIR', range}

        Relationships:
        - (ACUnit)-[:SERVICES]->(Room)
        - (Room)-[:HAS_SENSOR]->(TemperatureSensor|OccupancySensor)
        - (TemperatureSensor|OccupancySensor)-[:MONITORS]->(Room)
        - (ACUnit)-[:LOCATED_IN]->(MechanicalRoom)
        - (ACUnit)-[:MONITORS_SENSOR]->(TemperatureSensor|OccupancySensor)

        Sample Data:
        - Rooms: D001-D006 (dormitory), M001-M002 (mechanical) - Total: 8 rooms
        - AC Units: AC001, AC002
        - Sensors: T001-T006 (temperature), O001-O006 (occupancy)

        IMPORTANT Room Counting:
        - For "how many rooms" queries, count BOTH Room and MechanicalRoom nodes
        - Use: MATCH (r:Room) WITH count(r) as dorm_count MATCH (m:MechanicalRoom) RETURN dorm_count + count(m) as total_rooms, dorm_count, count(m) as mechanical_count
        - Total should always be 8 rooms (6 dormitory + 2 mechanical)

        IMPORTANT Query Guidelines:
        - For AC unit queries, use exact property matching: {id: 'AC001'} not {id: "AC001"}
        - Always use single quotes for string literals in Cypher
        - For "which rooms are serviced by AC001", use: MATCH (ac:ACUnit {id: 'AC001'})-[r:SERVICES]->(room:Room) RETURN ac.id AS ac_unit, room.id AS room_id, room.type AS room_type, room.side AS room_side
        - For "which AC services room D003", use: MATCH (ac:ACUnit)-[:SERVICES]->(room:Room {id: 'D003'})
        - Use collect() to gather multiple results: collect(room.id) as rooms_serviced
        - NEVER use avg(), sum(), or mathematical functions on string properties like accuracy, unit, technology
        - Only return basic properties: id, type, side - avoid calculated fields
        - For temperature comparisons, only return room structure, not sensor data calculations
        - NEVER use rand(), datetime(), or any function that generates fake data in Neo4j queries
        - For daily temperature queries, Neo4j should only return room and sensor information, not temperature values
        - Temperature values should come from InfluxDB time series data only
        - Always include relationship variable [r:SERVICES] instead of just [:SERVICES] for better query reliability
        - For AC-room relationships, use pattern: (ac:ACUnit {id: 'AC001'})-[r:SERVICES]->(room:Room)
        """

        prompt = f"""
        Based on the Neo4j database schema below, generate a Cypher query to answer the user's question.

        {schema_info}

        User Question: "{description}"

        Generate ONLY the Cypher query, no explanations. The query should:
        1. Return meaningful column names
        2. Include appropriate ORDER BY clauses
        3. Use proper Cypher syntax
        4. Return actual data that answers the user's question

        Cypher Query:
        """

        try:
            response = await self.llm.ainvoke(prompt)
            cypher_query = response.content.strip()

            # Clean up the response to extract just the query
            if "```" in cypher_query:
                # Extract query from code blocks
                lines = cypher_query.split('\n')
                query_lines = []
                in_code_block = False
                for line in lines:
                    if line.strip().startswith('```'):
                        in_code_block = not in_code_block
                        continue
                    if in_code_block or (not in_code_block and line.strip().upper().startswith(('MATCH', 'RETURN', 'WITH', 'WHERE', 'ORDER', 'LIMIT'))):
                        query_lines.append(line)
                cypher_query = '\n'.join(query_lines).strip()

            logger.info(f"Generated Cypher query: {cypher_query}")
            return cypher_query

        except Exception as e:
            logger.error(f"Failed to generate Cypher query: {e}")
            # Fallback to a safe default query
            return """
            MATCH (n)
            RETURN labels(n) as node_type, count(n) as count
            ORDER BY node_type
            """



    async def _generate_flux_query(self, description: str) -> str:
        """Generate Flux query using LLM based on user description"""

        bucket = settings.INFLUXDB_BUCKET

        # Database schema information for the LLM
        schema_info = f"""
        InfluxDB Database Schema:

        Bucket: {bucket}

        Measurements:
        - temperature: Temperature sensor readings
          - Fields: value (float), unit (string)
          - Tags: sensor_id, room, side

        - occupancy: Occupancy sensor readings
          - Fields: value (int 0/1), occupied (boolean)
          - Tags: sensor_id, room, side, student_profile

        Sample Data:
        - Rooms: D001-D006
        - Sides: sunny, shaded
        - Sensor IDs: T001-T006 (temperature), O001-O006 (occupancy)
        - Student Profiles: full-time, working-night
        - Time range: Last 7 days with 5-minute intervals

        Student Profile Patterns:
        - full-time: In dorm 7-9am, 1-3pm, 8-10pm, night (rooms D001, D003, D005)
        - working-night: In dorm 6-8am, 4-6pm, 9-11pm, night (rooms D002, D004, D006)

        IMPORTANT Flux Syntax Rules:
        - Use r["_measurement"] for measurement names
        - Use r["_field"] for field names
        - Use r["_value"] for field values (NOT r["value"])
        - Use r["room"] for room tags
        - Use r["sensor_id"] for sensor tags
        - Use r["side"] for side tags
        - Always use -7d for time ranges to ensure data is found

        Common Flux Functions:
        - range(start: -7d) for time filtering (use 7 days to ensure data)
        - filter(fn: (r) => r["_measurement"] == "temperature") for measurement filtering
        - filter(fn: (r) => r["_field"] == "value") for field filtering
        - filter(fn: (r) => r["_value"] > 25.0) for value filtering (use _value not value)
        - group(columns: ["room"]) for grouping
        - aggregateWindow(every: 1h, fn: mean) for time aggregation
        - sort(columns: ["_time"]) for sorting

        Student Profile Queries:
        - For student profile filtering: filter(fn: (r) => r["student_profile"] == "full-time")
        - For occupancy pattern analysis: group(columns: ["student_profile", "room"])
        - For time-based student analysis: use time filters matching their schedules
        - Full-time students: active 7-9am, 1-3pm, 8-10pm, night
        - Working-night students: active 6-8am, 4-6pm, 9-11pm, night

        CRITICAL Flux Rules (MUST FOLLOW):
        - NEVER use max(), min(), mean(), sum() on string columns (room, sensor_id, side, student_profile)
        - ONLY use aggregation functions on _value field: max(column: "_value")
        - Use group() for grouping by tags, not for aggregation
        - For finding highest temperature: max(column: "_value") then group by room
        - For room-specific queries: filter by room first, then aggregate _value
        """

        prompt = f"""
        Based on the InfluxDB database schema below, generate a Flux query to answer the user's question.

        {schema_info}

        User Question: "{description}"

        Generate ONLY the Flux query, no explanations. The query should:
        1. Use the correct bucket name: {bucket}
        2. Include appropriate time ranges
        3. Filter for the correct measurement and field
        4. Use proper Flux syntax
        5. Return meaningful data that answers the user's question

        Flux Query:
        """

        try:
            response = await self.llm.ainvoke(prompt)
            flux_query = response.content.strip()

            # Clean up the response to extract just the query
            if "```" in flux_query:
                # Extract query from code blocks
                lines = flux_query.split('\n')
                query_lines = []
                in_code_block = False
                for line in lines:
                    if line.strip().startswith('```'):
                        in_code_block = not in_code_block
                        continue
                    if in_code_block or (not in_code_block and line.strip().startswith(('from(', '|>', 'import'))):
                        query_lines.append(line)
                flux_query = '\n'.join(query_lines).strip()

            logger.info(f"Generated Flux query: {flux_query}")
            return flux_query

        except Exception as e:
            logger.error(f"Failed to generate Flux query: {e}")
            # Fallback to a safe default query
            return f'''
from(bucket: "{bucket}")
  |> range(start: -24h)
  |> filter(fn: (r) => r["_measurement"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "value")
  |> group(columns: ["room"])
  |> mean(column: "_value")
            '''

    async def _generate_predictive_flux_query(self, description: str) -> str:
        """Generate Flux query for historical data needed for predictions"""
        bucket = "sensor_data"

        # Extract room number from the query
        import re
        room_match = re.search(r'\b[dD](\d{3})\b', description)
        room_filter = ""
        if room_match:
            room_number = room_match.group(1)
            room_id = f"D{room_number}"
            room_filter = f'  |> filter(fn: (r) => r["room"] == "{room_id}")\n'

        query_lower = description.lower()

        if "occupancy" in query_lower:
            # Occupancy prediction query - get time series data for pattern analysis
            # Try without field filter first to see if occupancy measurement exists
            return f'''
from(bucket: "{bucket}")
  |> range(start: -7d)
  |> filter(fn: (r) => r["_measurement"] == "occupancy")
{room_filter}  |> sort(columns: ["_time"])
  |> limit(n: 100)
            '''
        else:
            # Temperature prediction query (default) - get time series data for pattern analysis
            return f'''
from(bucket: "{bucket}")
  |> range(start: -7d)
  |> filter(fn: (r) => r["_measurement"] == "temperature")
  |> filter(fn: (r) => r["_field"] == "value")
{room_filter}  |> aggregateWindow(every: 1h, fn: mean, createEmpty: false)
  |> sort(columns: ["_time"])
            '''

    async def _generate_prediction(self, user_query: str, historical_data: List[Dict]) -> str:
        """Generate prediction based on historical data patterns"""
        if not historical_data:
            return "Insufficient historical data available for prediction."

        # Format historical data for analysis
        historical_summary = self._format_influxdb_results(historical_data)

        prediction_prompt = f"""
        Based on the historical data below from the last 7 days, provide a data-driven prediction for: "{user_query}"

        Historical Data (last 7 days):
        {historical_summary}

        IMPORTANT: You have REAL historical data above. Use this actual data to make your prediction.

        Analyze the patterns in this data and provide:
        1. **Identified trends and patterns** from the actual data shown above
        2. **Data-driven prediction** for tomorrow based on the patterns you observe
        3. **Confidence level** based on data consistency (high/medium/low)
        4. **Key factors** influencing the prediction from the actual data

        Use the actual data patterns to make realistic predictions. Consider:
        - Daily occupancy/temperature cycles shown in the data
        - Weekly patterns (if visible in 7 days)
        - Room-specific characteristics from the data
        - Student occupancy patterns (full-time vs working-night students)
        - Time-of-day patterns visible in the historical data

        Provide a specific numerical prediction with supporting analysis based on the actual data provided.
        """

        try:
            response = await self.llm.ainvoke(prediction_prompt)
            return response.content

        except Exception as e:
            logger.error(f"Failed to generate prediction: {e}")
            return f"Unable to generate prediction due to processing error: {str(e)}"

    def _format_neo4j_results(self, results: List[Dict]) -> str:
        """Format Neo4j results for LLM"""
        if not results:
            return "No data found in the graph database."

        formatted = "Graph database results:\n"
        for result in results[:10]:  # Limit results
            formatted += f"- {result}\n"
        return formatted

    def _format_influxdb_results(self, results: List[Dict]) -> str:
        """Format InfluxDB results for LLM"""
        if not results:
            return "No data found in the time series database."

        # Group results by measurement type for better analysis
        temp_data = [r for r in results if r.get('_measurement') == 'temperature']
        occupancy_data = [r for r in results if r.get('_measurement') == 'occupancy']

        formatted = "Time series database results:\n"

        if temp_data:
            # Check if we have time-based data (daily/hourly breakdown)
            has_time_data = any('_time' in result for result in temp_data)

            if has_time_data:
                # Preserve time-based structure for daily/hourly data
                formatted += "Temperature data (time series):\n"
                for result in temp_data:
                    room = result.get('room', 'Unknown')
                    temp = result.get('_value')
                    time_str = result.get('_time', 'Unknown time')

                    if temp is not None and isinstance(temp, (int, float)):
                        # Format time for readability (extract date)
                        if 'T' in str(time_str):
                            date_part = str(time_str).split('T')[0]
                            formatted += f"- {room} on {date_part}: {temp:.2f}°C\n"
                        else:
                            formatted += f"- {room}: {temp:.2f}°C at {time_str}\n"
            else:
                # Calculate averages for non-time-based data
                room_temps = {}
                side_temps = {"sunny": [], "shaded": []}

                for result in temp_data:
                    room = result.get('room', 'Unknown')
                    temp = result.get('_value')
                    side = result.get('side', 'unknown')

                    # Only process valid temperature values
                    if temp is not None and isinstance(temp, (int, float)):
                        if room not in room_temps:
                            room_temps[room] = []
                        room_temps[room].append(temp)

                        if side in side_temps:
                            side_temps[side].append(temp)

                formatted += "Temperature data:\n"
                for room, temps in room_temps.items():
                    avg_temp = sum(temps) / len(temps)
                    formatted += f"- {room}: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

                # Add side-based analysis if side data is available
                if any(side_temps.values()):
                    formatted += "\nTemperature by building side:\n"
                    for side, temps in side_temps.items():
                        if temps:
                            avg_temp = sum(temps) / len(temps)
                            formatted += f"- {side.title()} side: {avg_temp:.1f}°C (avg from {len(temps)} readings)\n"

        if occupancy_data:
            # Check if we have time-based data (hourly/daily breakdown)
            has_time_data = any('_time' in result for result in occupancy_data)

            if has_time_data:
                # Show time-series occupancy data for pattern analysis
                formatted += "Occupancy data (time series):\n"

                # Group by day and hour for pattern analysis
                daily_patterns = {}
                hourly_patterns = {}

                for result in occupancy_data:
                    room = result.get('room', 'Unknown')
                    occupied = result.get('_value')
                    time_str = result.get('_time', 'Unknown time')
                    student_profile = result.get('student_profile', 'unknown')

                    if occupied is not None and isinstance(occupied, (int, float)) and 'T' in str(time_str):
                        # Extract date and hour
                        date_part = str(time_str).split('T')[0]
                        hour_part = str(time_str).split('T')[1][:2] if 'T' in str(time_str) else '00'

                        # Store daily patterns
                        if date_part not in daily_patterns:
                            daily_patterns[date_part] = []
                        daily_patterns[date_part].append(occupied)

                        # Store hourly patterns
                        if hour_part not in hourly_patterns:
                            hourly_patterns[hour_part] = []
                        hourly_patterns[hour_part].append(occupied)

                        # Show individual readings for detailed analysis
                        formatted += f"- {room} on {date_part} at {hour_part}:00: {occupied*100:.0f}% occupied"
                        if student_profile != 'unknown':
                            formatted += f" ({student_profile} student)"
                        formatted += "\n"

                # Add daily pattern summary
                if daily_patterns:
                    formatted += "\nDaily occupancy patterns:\n"
                    for date, occupancy_list in sorted(daily_patterns.items()):
                        avg_occupancy = (sum(occupancy_list) / len(occupancy_list)) * 100
                        formatted += f"- {date}: {avg_occupancy:.0f}% average occupancy ({len(occupancy_list)} readings)\n"

                # Add hourly pattern summary
                if hourly_patterns:
                    formatted += "\nHourly occupancy patterns:\n"
                    for hour, occupancy_list in sorted(hourly_patterns.items()):
                        avg_occupancy = (sum(occupancy_list) / len(occupancy_list)) * 100
                        formatted += f"- {hour}:00 hour: {avg_occupancy:.0f}% average occupancy ({len(occupancy_list)} readings)\n"

            else:
                # Calculate occupancy rates with student profile breakdown (fallback)
                room_occupancy = {}
                profile_occupancy = {}

                for result in occupancy_data:
                    room = result.get('room', 'Unknown')
                    occupied = result.get('_value')
                    student_profile = result.get('student_profile', 'unknown')

                    # Only process valid occupancy values
                    if occupied is not None and isinstance(occupied, (int, float)):
                        # Room-based occupancy
                        if room not in room_occupancy:
                            room_occupancy[room] = []
                        room_occupancy[room].append(occupied)

                        # Student profile-based occupancy
                        if student_profile not in profile_occupancy:
                            profile_occupancy[student_profile] = []
                        profile_occupancy[student_profile].append(occupied)

                formatted += "Occupancy data:\n"
                for room, occupancy in room_occupancy.items():
                    rate = (sum(occupancy) / len(occupancy)) * 100
                    formatted += f"- {room}: {rate:.0f}% occupancy rate ({len(occupancy)} readings)\n"

                # Add student profile analysis if available
                if len(profile_occupancy) > 1:  # Only show if we have multiple profiles
                    formatted += "\nOccupancy by student profile:\n"
                    for profile, occupancy in profile_occupancy.items():
                        if profile != 'unknown':
                            rate = (sum(occupancy) / len(occupancy)) * 100
                            formatted += f"- {profile.title()} students: {rate:.0f}% occupancy rate ({len(occupancy)} readings)\n"

        return formatted




# Global instance
llm_manager = LLMManager()
