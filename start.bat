@echo off
echo === DormIQ Analytics Backend Startup ===
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found!
    echo Please copy .env.example to .env and configure it.
    echo.
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Start the server
echo.
echo Starting DormIQ Analytics Backend...
echo Server will be available at: http://localhost:8000
echo API documentation will be available at: http://localhost:8000/docs
echo Press Ctrl+C to stop the server
echo.

python main.py

pause
