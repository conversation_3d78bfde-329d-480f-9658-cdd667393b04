"""
Database connection managers for Neo4j and InfluxDB
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio

from neo4j import AsyncGraphDatabase, AsyncDriver
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
from influxdb_client.client.write_api_async import WriteApiAsync
from influxdb_client.client.query_api_async import QueryApiAsync

from .config import settings

logger = logging.getLogger(__name__)


class Neo4jManager:
    """Neo4j database connection manager"""

    def __init__(self):
        self.driver: Optional[AsyncDriver] = None
        self.connected = False

    async def connect(self):
        """Establish connection to Neo4j"""
        try:
            self.driver = AsyncGraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
            )

            # Test connection
            await self.driver.verify_connectivity()
            self.connected = True
            logger.info("Connected to Neo4j successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise

    async def close(self):
        """Close Neo4j connection"""
        if self.driver:
            await self.driver.close()
            self.connected = False
            logger.info("Neo4j connection closed")

    async def health_check(self) -> Dict[str, Any]:
        """Check Neo4j health"""
        if not self.connected:
            return {"status": "disconnected", "error": "No connection"}

        if not self.driver:
            return {"status": "disconnected", "error": "No driver"}

        try:
            async with self.driver.session(database=settings.NEO4J_DATABASE) as session:
                result = await session.run("RETURN 1 as test")
                await result.single()
                return {"status": "connected", "database": settings.NEO4J_DATABASE}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def execute_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """Execute Cypher query"""
        if not self.connected or not self.driver:
            raise Exception("Neo4j not connected")

        try:
            async with self.driver.session(database=settings.NEO4J_DATABASE) as session:
                result = await session.run(query, parameters or {})
                records = []
                async for record in result:
                    records.append(dict(record))
                return records
        except Exception as e:
            logger.error(f"Neo4j query failed: {e}")
            raise


class InfluxDBManager:
    """InfluxDB connection manager"""

    def __init__(self):
        self.client: Optional[InfluxDBClientAsync] = None
        self.write_api: Optional[WriteApiAsync] = None
        self.query_api: Optional[QueryApiAsync] = None
        self.connected = False

    async def connect(self):
        """Establish connection to InfluxDB"""
        try:
            self.client = InfluxDBClientAsync(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )

            self.write_api = self.client.write_api()
            self.query_api = self.client.query_api()
            self.delete_api = self.client.delete_api()

            # Test connection
            await self.client.ping()
            self.connected = True
            logger.info("Connected to InfluxDB successfully")

        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            raise

    async def close(self):
        """Close InfluxDB connection"""
        if self.client:
            await self.client.close()
            self.connected = False
            logger.info("InfluxDB connection closed")

    async def health_check(self) -> Dict[str, Any]:
        """Check InfluxDB health"""
        if not self.connected:
            return {"status": "disconnected", "error": "No connection"}

        if not self.client:
            return {"status": "disconnected", "error": "No client"}

        try:
            await self.client.ping()
            return {"status": "connected", "bucket": settings.INFLUXDB_BUCKET}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def execute_query(self, query: str) -> List[Dict]:
        """Execute Flux query"""
        if not self.connected or not self.query_api:
            raise Exception("InfluxDB not connected")

        try:
            tables = await self.query_api.query(query, org=settings.INFLUXDB_ORG)
            records = []
            for table in tables:
                for record in table.records:
                    records.append(record.values)
            return records
        except Exception as e:
            logger.error(f"InfluxDB query failed: {e}")
            raise


# Global instances
neo4j_manager = Neo4jManager()
influxdb_manager = InfluxDBManager()
